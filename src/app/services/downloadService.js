const converter = require('json-2-csv');
class DownloadService {
  constructor(container) {
    const {
      db,
      utils,
      entities,
      clientErrors,
      leaveRepository,
      fastify,
      requisitionRepository,
      nonRequisitionRepository,
      historyRepository,
      itemRepository,
      ofmItemListRepository,
      constants,
    } = container;

    this.db = db;
    this.utils = utils;
    this.clientErrors = clientErrors;
    this.entities = entities;
    this.leaveRepository = leaveRepository;
    this.fastify = fastify;
    this.Sequelize = db.Sequelize;
    this.requisitionRepository = requisitionRepository;
    this.nonRequisitionRepository = nonRequisitionRepository;
    this.historyRepository = historyRepository;
    this.itemRepository = itemRepository;
    this.ofmItemListRepository = ofmItemListRepository;
    this.constants = constants;
  }

  #generateNewLine() {
    return '\r\n';
  }

  #generateDateLine() {
    const formattedDate = this.utils.formatDateYYYYMMDD();
    const dateLine = { [`Extract as of ${formattedDate}`]: '' };

    return converter.json2csv(dateLine);
  }

  #formatExcelFile(...lines) {
    return lines.join('');
  }

  async downloadDashboardExcel(payload) {
    const {
      limit = 1_000_000,
      requestType = 'all',
      userId,
      ...options
    } = payload;
    const data = await this.requisitionRepository.getAllRequisitionsV2({
      limit,
      userId,
      ...options,
    });

    const transformData = [];

    data[requestType]?.forEach((data) =>
      transformData.push({
        [`Ref No`]: `${data.ref_number}`,
        Type: `${data.doc_type}`,
        Requester: `${data.requestor_name}`,
        Company: `${data.company_name}`,
        Department: `${data.department_name || '---'}`,
        Project: `${data.project_name || '---'}`,
        ['Last Updated']: `${this.utils.formatToMonthDayYear(data.updated_at)}`,
        Status: data.status.replaceAll('_', ' ').toUpperCase(),
      }),
    );

    return this.#formatExcelFile(
      this.#generateDateLine(),
      this.#generateNewLine(),
      converter.json2csv(transformData),
    );
  }

  generateExcelFile(data) {
    return this.#formatExcelFile(
      this.#generateDateLine(),
      this.#generateNewLine(),
      converter.json2csv(data),
    );
  }

  async downloadItemHistory(payload) {
    const historyData = await this.getItemPurchaseHistoryReport(payload);

    if (!historyData || historyData.length === 0) {
      // Return empty CSV if no data
      return this.#formatExcelFile(
        this.#generateDateLine(),
        this.#generateNewLine(),
        converter.json2csv([]),
      );
    }

    const transformData = [];

    for (const data of historyData) {
      const transformedData = {
        'RS Number': data.rsNumber || ' --- ',
        'Supplier ': data.supplierName || ' --- ',
        'Price per Unit': data.pricePerUnit || ' --- ',
        'Qty ': data.quantity || ' --- ',
        'Date Purchased': data.datePurchased
          ? this.utils.formatToMonthDayYear(data.datePurchased)
          : ' --- ',
      };

      transformData.push(transformedData);
    }

    // Generate CSV file
    return this.#formatExcelFile(
      this.#generateDateLine(),
      this.#generateNewLine(),
      converter.json2csv(transformData),
    );
  }

  async getItemPurchaseHistoryReport(params) {
    const {
      id,
      type,
      filterBy = null,
      sortBy = { updatedAt: 'desc' },
    } = params;

    const order = Object.entries(sortBy).map(([key, value]) => [
      key,
      value.toUpperCase(),
    ]);

    const { data: histories } = await this.historyRepository.getAllItemsHistory(
      {
        id,
        type,
        filterBy,
        order,
        paginate: false,
      },
    );

    // If no histories, return immediately
    if (!histories.length) return [];

    // Get valid PO statuses
    const { PO_STATUS } = this.constants.purchaseOrder;
    const validStatuses = [
      PO_STATUS.FOR_SENDING,
      PO_STATUS.FOR_DELIVERY,
      PO_STATUS.CLOSED_PO,
      PO_STATUS.CANCELLED_PO,
    ]
      .map((status) => `'${status}'`)
      .join(', ');

    // PO data query (without joining to histories)
    const poDataQuery = `
    WITH supplier_data AS (
      SELECT 
        po.id as po_id,
        CASE 
          WHEN po.supplier_name IS NOT NULL THEN po.supplier_name
          WHEN po.supplier_type = 'company' THEN c.name
          WHEN po.supplier_type = 'project' THEN p.name
          WHEN po.supplier_type = 'supplier' THEN s.name
          ELSE 'N/A'
        END as supplier_name
      FROM purchase_orders po
      LEFT JOIN companies c ON po.supplier_id = c.id AND po.supplier_type = 'company'
      LEFT JOIN projects p ON po.supplier_id = p.id AND po.supplier_type = 'project'
      LEFT JOIN suppliers s ON po.supplier_id = s.id AND po.supplier_type = 'supplier'
    )
    SELECT 
      ril.item_id,
      CONCAT('RS-', CASE WHEN LENGTH(CAST(r.company_code AS VARCHAR)) = 1 THEN CONCAT('0', r.company_code) ELSE CAST(r.company_code AS VARCHAR) END, r.rs_letter, r.rs_number) as rs_number,
      CONCAT(
        CASE 
          WHEN LENGTH(CAST(r.company_code AS VARCHAR)) = 1 THEN CONCAT('0', r.company_code) 
          ELSE CAST(r.company_code AS VARCHAR) 
        END, 
        r.rs_letter, 
        r.rs_number
      ) AS rs_number_raw,
      sd.supplier_name,
      CASE 
        WHEN cis.discount_type = 'percent' THEN 
          ROUND(CAST(cis.unit_price * (1 - cis.discount_value / 100) AS numeric), 2)
        ELSE  
          ROUND(CAST(cis.unit_price - (cis.discount_value / poi.quantity_purchased) AS numeric), 2)
      END as unit_price,
      poi.quantity_purchased,
      ril.quantity as requested_quantity, 
      cis.quantity as approved_quantity,
      po.created_at as date_purchased,
      r.id as requisition_id
    FROM requisition_item_lists ril
    JOIN purchase_order_items poi ON poi.requisition_item_list_id = ril.id
    JOIN purchase_orders po ON poi.purchase_order_id = po.id
    JOIN requisitions r ON po.requisition_id = r.id
    JOIN canvass_item_suppliers cis ON poi.canvass_item_supplier_id = cis.id
    JOIN supplier_data sd ON po.id = sd.po_id
    WHERE ril.item_id = :id
    AND po.status IN (${validStatuses})
  `;

    const poData = await this.db.sequelize.query(poDataQuery, {
      replacements: { id },
      type: this.db.Sequelize.QueryTypes.SELECT,
    });

    // Match each history record to PO data
    const formattedResults = histories.map((history) => {
      // Find matching PO using rs number and requested quantity
      const purchaseOrderitemHistory = poData.filter(
        (po) =>
          po.rs_number_raw === history.rsNumberRaw &&
          po.requested_quantity === history.quantityRequested,
      )[0];

      return {
        id: history.id,
        rsNumber: purchaseOrderitemHistory?.rs_number || history.rsNumber,
        supplierName: purchaseOrderitemHistory?.supplier_name || ' --- ',
        pricePerUnit: purchaseOrderitemHistory?.unit_price
          ? parseFloat(purchaseOrderitemHistory.unit_price).toFixed(2) + ''
          : history.price
            ? parseFloat(history.price).toFixed(2) + ''
            : ' --- ',
        quantity: history.quantityDelivered
          ? parseFloat(history.quantityDelivered).toFixed(3)
          : history.quantityRequested
            ? parseFloat(history.quantityRequested).toFixed(3)
            : ' --- ',
        datePurchased: purchaseOrderitemHistory?.date_purchased
          ? purchaseOrderitemHistory.date_purchased
          : null,
        requisitionId: purchaseOrderitemHistory
          ? purchaseOrderitemHistory.requisition_id
          : '',
      };
    });

    return formattedResults;
  }

  #parseQueryParams(searchBy, filterBy, sortBy) {
    const parseParam = (param, paramName) => {
      if (!param) return {};

      try {
        return typeof param === 'string' ? JSON.parse(param) : param;
      } catch (error) {
        this.fastify.log.warn(`Invalid ${paramName} format`);
        return {};
      }
    };

    return {
      parsedSearchBy: parseParam(searchBy, 'searchBy'),
      parsedFilterBy: parseParam(filterBy, 'filterBy'),
      parsedSortBy: parseParam(sortBy, 'sortBy'),
    };
  }

  async downloadOfmItems(payload) {
    const { searchBy, filterBy, sortBy } = payload;
    const { parsedSearchBy, parsedFilterBy, parsedSortBy } =
      this.#parseQueryParams(searchBy, filterBy, sortBy);

    // Build WHERE conditions
    let whereConditions = [];
    let replacements = {};

    // Handle filterBy (global search)
    if (parsedFilterBy) {
      if (typeof parsedFilterBy === 'string') {
        const searchTerm = parsedFilterBy.trim();
        if (searchTerm) {
          whereConditions.push(`(
            i.itm_des ILIKE :searchTerm OR 
            i.item_cd ILIKE :searchTerm OR 
            i.acct_cd ILIKE :searchTerm
          )`);
          replacements.searchTerm = `%${searchTerm}%`;
        }
      } else {
        // Handle object filterBy
        const filterConditions =
          this.utils.buildFilterWhereClause(parsedFilterBy);

        if (filterConditions.itmDes) {
          whereConditions.push('i.itm_des ILIKE :itmDes');
          replacements.itmDes = `%${filterConditions.itmDes}%`;
        }
        if (filterConditions.itemCd) {
          whereConditions.push('i.item_cd = :itemCd');
          replacements.itemCd = filterConditions.itemCd;
        }
        if (filterConditions.acctCd) {
          whereConditions.push('i.acct_cd = :acctCd');
          replacements.acctCd = filterConditions.acctCd;
        }
        if (filterConditions.unit) {
          whereConditions.push('i.unit = :unit');
          replacements.unit = filterConditions.unit;
        }
        if (filterConditions.trade) {
          whereConditions.push('t.trade_name ILIKE :trade');
          replacements.trade = `%${filterConditions.trade}%`;
        }
      }
    }

    // Handle searchBy conditions
    if (parsedSearchBy) {
      if (parsedSearchBy.itmDes) {
        whereConditions.push('i.itm_des ILIKE :searchItmDes');
        replacements.searchItmDes = `%${parsedSearchBy.itmDes}%`;
      }
      if (parsedSearchBy.itemCd) {
        whereConditions.push('i.item_cd ILIKE :searchItemCd');
        replacements.searchItemCd = `%${parsedSearchBy.itemCd}%`;
      }
      if (parsedSearchBy.acctCd) {
        whereConditions.push('i.acct_cd ILIKE :searchAcctCd');
        replacements.searchAcctCd = `%${parsedSearchBy.acctCd}%`;
      }
      if (parsedSearchBy.unit) {
        whereConditions.push('i.unit ILIKE :searchUnit');
        replacements.searchUnit = `%${parsedSearchBy.unit}%`;
      }
    }

    // Build ORDER BY clause
    let orderClause = 'ORDER BY i.itm_des ASC';
    if (parsedSortBy && Object.keys(parsedSortBy).length > 0) {
      const fieldMapping = {
        itemCd: 'i.item_cd',
        itmDes: 'i.itm_des',
        unit: 'i.unit',
        acctCd: 'i.acct_cd',
        remainingGfq: 'i.remaining_gfq',
        isSteelbars: 'i.is_steelbars',
        trade: 't.trade_name',
        createdAt: 'i.created_at',
        updatedAt: 'i.updated_at',
      };

      const orderParts = Object.entries(parsedSortBy).map(
        ([field, direction]) => {
          const mappedField = fieldMapping[field] || field;
          return `${mappedField} ${direction.toUpperCase()}`;
        },
      );

      if (orderParts.length > 0) {
        orderClause = `ORDER BY ${orderParts.join(', ')}`;
      }
    }

    // Build final query
    const whereClause =
      whereConditions.length > 0
        ? `WHERE ${whereConditions.join(' AND ')}`
        : '';

    const query = `
      SELECT DISTINCT
        i.item_cd as "itemCd",
        i.itm_des as "itmDes",
        i.unit,
        i.acct_cd as "acctCd",
        i.gfq,
        i.remaining_gfq as "remainingGfq",
        i.is_steelbars as "isSteelbars",
        t.trade_name as "tradeName",
        s.weight as "steelbarWeight",
        COALESCE(
          (SELECT c.name 
           FROM ofm_list_items oli2 
           JOIN ofm_item_lists oil2 ON oli2.ofm_list_id = oil2.id 
           JOIN companies c ON oil2.company_code = c.code 
           WHERE oli2.ofm_item_id = i.id 
           LIMIT 1), 
          '---'
        ) as "companyName",
        COALESCE(
          (SELECT p.name 
           FROM ofm_list_items oli2 
           JOIN ofm_item_lists oil2 ON oli2.ofm_list_id = oil2.id 
           JOIN projects p ON oil2.project_code = p.code 
           WHERE oli2.ofm_item_id = i.id 
           LIMIT 1), 
          '---'
        ) as "projectName"
      FROM items i
      INNER JOIN trades t ON i.trade_code = t.trade_code
      LEFT JOIN steelbars s ON s.ofm_acctcd = i.acct_cd
      LEFT JOIN ofm_list_items oli ON oli.ofm_item_id = i.id
      LEFT JOIN ofm_item_lists oil ON oli.ofm_list_id = oil.id
      LEFT JOIN companies c ON oil.company_code = c.code
      LEFT JOIN projects p ON oil.project_code = p.code
      ${whereClause}
      ${orderClause}
    `;

    const data = await this.db.sequelize.query(query, {
      replacements,
      type: this.db.Sequelize.QueryTypes.SELECT,
    });

    // const uniqueData = [];
    // const seenItemCodes = new Set();

    // for (const item of data) {
    //   if (!seenItemCodes.has(item.acctCd)) {
    //     seenItemCodes.add(item.acctCd);
    //     uniqueData.push(item);
    //   }
    // }

    const transformData = data.map((item) => ({
      'Item Code': item.itemCd || '---',
      'Item Description': item.itmDes || '---',
      Unit: item.unit || '---',
      'Account Code': item.acctCd || '---',
      GFQ: item.gfq || '---',
      Weight:
        item.steelbarWeight && item.steelbarUnit
          ? `${parseFloat(item.steelbarWeight).toFixed(3)} ${item.steelbarUnit}`
          : item.steelbarWeight
            ? `${parseFloat(item.steelbarWeight).toFixed(3)}`
            : '---',
      Steelbars: item.isSteelbars ? 'Yes' : 'No',
      Trade: item.tradeName || '---',
    }));

    let csvData = converter.json2csv(transformData, {
      delimiter: { field: ',' },
      quote: '',
    });

    // Replace escaped quotes in CSV: "" becomes "
    csvData = csvData.replace(/"([^"]*""[^"]*)"(?=,|$)/g, '$1');
    csvData = csvData.replace(/""/g, '"');

    return this.#formatExcelFile(
      this.#generateDateLine(),
      this.#generateNewLine(),
      csvData,
    );
  }

  async downloadOfmList(payload) {
    const { searchBy, filterBy, sortBy } = payload;
    const { parsedSearchBy, parsedFilterBy, parsedSortBy } =
      this.#parseQueryParams(searchBy, filterBy, sortBy);

    // Build WHERE conditions
    let whereConditions = [];
    let replacements = {};

    // Handle filterBy (global search)
    if (parsedFilterBy) {
      if (typeof parsedFilterBy === 'string') {
        const searchTerm = parsedFilterBy.trim();
        if (searchTerm) {
          whereConditions.push(`(
            oil.list_name ILIKE :searchTerm OR 
            c.name ILIKE :searchTerm OR 
            p.name ILIKE :searchTerm OR 
            t.trade_name ILIKE :searchTerm
          )`);
          replacements.searchTerm = `%${searchTerm}%`;
        }
      } else if (typeof parsedFilterBy === 'object') {
        Object.entries(parsedFilterBy).forEach(([key, value]) => {
          if (key === 'listName') {
            whereConditions.push('oil.list_name ILIKE :filter_listName');
            replacements.filter_listName = `%${value}%`;
          } else if (key === 'company') {
            whereConditions.push('c.name ILIKE :filter_company');
            replacements.filter_company = `%${value}%`;
          } else if (key === 'project') {
            whereConditions.push('p.name ILIKE :filter_project');
            replacements.filter_project = `%${value}%`;
          } else if (key === 'trade') {
            whereConditions.push('t.trade_name ILIKE :filter_trade');
            replacements.filter_trade = `%${value}%`;
          }
        });
      }
    }

    // Handle searchBy conditions
    if (parsedSearchBy && typeof parsedSearchBy === 'object') {
      Object.entries(parsedSearchBy).forEach(([key, value]) => {
        if (key === 'listName') {
          whereConditions.push('oil.list_name ILIKE :search_listName');
          replacements.search_listName = `%${value}%`;
        } else if (key === 'company') {
          whereConditions.push('c.name ILIKE :search_company');
          replacements.search_company = `%${value}%`;
        } else if (key === 'project') {
          whereConditions.push('p.name ILIKE :search_project');
          replacements.search_project = `%${value}%`;
        } else if (key === 'trade') {
          whereConditions.push('t.trade_name ILIKE :search_trade');
          replacements.search_trade = `%${value}%`;
        }
      });
    }

    // Build ORDER BY clause
    let orderClause = 'ORDER BY oil.list_name ASC';
    if (parsedSortBy && Object.keys(parsedSortBy).length > 0) {
      const fieldMapping = {
        listName: 'oil.list_name',
        company: 'c.name',
        project: 'p.name',
        trade: 't.trade_name',
        createdAt: 'oil.created_at',
        updatedAt: 'oil.updated_at',
      };

      const orderParts = Object.entries(parsedSortBy).map(
        ([field, direction]) => {
          const mappedField = fieldMapping[field] || `oil.${field}`;
          return `${mappedField} ${direction.toUpperCase()}`;
        },
      );

      if (orderParts.length > 0) {
        orderClause = `ORDER BY ${orderParts.join(', ')}`;
      }
    }

    // Build final query
    const whereClauseStr =
      whereConditions.length > 0
        ? `WHERE ${whereConditions.join(' AND ')}`
        : '';

    const query = `
      SELECT DISTINCT
        oil.id,
        oil.list_name as "listName",
        oil.company_code as "companyCode",
        oil.project_code as "projectCode",
        oil.trade_code as "tradeCode",
        oil.created_at as "createdAt",
        oil.updated_at as "updatedAt",
        c.id as "company.id",
        c.code as "company.code",
        c.name as "company.name",
        p.id as "project.id",
        p.code as "project.code",
        p.name as "project.name",
        t.id as "trade.id",
        t.trade_code as "trade.code",
        t.trade_name as "trade.name"
      FROM ofm_item_lists oil
      INNER JOIN companies c ON oil.company_code = c.code
      INNER JOIN projects p ON oil.project_code = p.code
      INNER JOIN trades t ON oil.trade_code = t.trade_code
      ${whereClauseStr}
      ${orderClause}
    `;

    const data = await this.db.sequelize.query(query, {
      replacements,
      type: this.db.Sequelize.QueryTypes.SELECT,
    });

    // Transform to match expected download format
    const transformData = data.map((item) => ({
      'List Name': item.listName || '---',
      Company: item['company.name'] || '---',
      Project: item['project.name'] || '---',
      Trade: item['trade.name'] || '---',
    }));

    return this.#formatExcelFile(
      this.#generateDateLine(),
      this.#generateNewLine(),
      converter.json2csv(transformData),
    );
  }
}

module.exports = DownloadService;
