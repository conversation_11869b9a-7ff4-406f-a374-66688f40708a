const { PDFDocument, StandardFonts } = require('pdf-lib');
const path = require('path');
const fs = require('fs');

class NonRequisitionService {
  constructor(container) {
    const {
      utils,
      fastify,
      constants,
      clientErrors,
      entities,
      nonRequisitionRepository,
      nonRequisitionItemRepository,
      nonRequisitionApproverRepository,
      nonRequisitionHistoryRepository,
      departmentApprovalRepository,
      userRepository,
      attachmentRepository,
      attachmentService,
      commentService,
      notificationService,
      approverService,
      db,
      citylandApiService,
      supplierRepository,
    } = container;

    this.utils = utils;
    this.fastify = fastify;
    this.constants = constants;
    this.clientErrors = clientErrors;
    this.entities = entities;
    this.nonRequisitionRepository = nonRequisitionRepository;
    this.nonRequisitionItemRepository = nonRequisitionItemRepository;
    this.nonRequisitionApproverRepository = nonRequisitionApproverRepository;
    this.nonRequisitionHistoryRepository = nonRequisitionHistoryRepository;
    this.departmentApprovalRepository = departmentApprovalRepository;
    this.userRepository = userRepository;
    this.attachmentRepository = attachmentRepository;
    this.attachmentService = attachmentService;
    this.commentService = commentService;
    this.notificationService = notificationService;
    this.approverService = approverService;
    this.db = db;
    this.citylandApiService = citylandApiService;
    this.supplierRepository = supplierRepository;
  }

  async createNonRS(payload = {}) {
    const {
      transaction,
      id: nonRsId,
      createdBy,
      groupDiscountPrice = 0,
      isDraft = false,
      itemList = [],
      ...restBody
    } = payload;
    const { NON_RS_STATUS } = this.constants.nonRS;
    const { MODELS } = this.constants.attachment;
    const numberField = isDraft ? 'draftNonRsNumber' : 'nonRsNumber';
    const nonRsStatus = isDraft
      ? NON_RS_STATUS.DRAFT
      : NON_RS_STATUS.FOR_APPROVAL;

    let nonRsDetails;
    let invoiceAttachment;
    if (nonRsId) {
      nonRsDetails = await this.getExistingNonRs(nonRsId);

      invoiceAttachment =
        await this.attachmentRepository.getAttachmentByModelId({
          id: nonRsId,
          model: MODELS.NON_RS_INVOICE,
        });
    }

    if (
      !restBody.invoiceAttachmentIds.length &&
      !invoiceAttachment?.data.length
    ) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'At least 1 invoice attachment is required for submission',
      });
    }

    const isNotAllowedToEdit =
      nonRsDetails && nonRsDetails.createdBy !== createdBy;
    if (isNotAllowedToEdit) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'You are not allowed to edit/submit this Non-RS Record',
      });
    }

    const { totalAmount, totalDiscount, totalDiscountedAmount } =
      await this.#calculateItemListSummary({
        isDraft,
        itemList,
        nonRsId: nonRsDetails?.id,
      });

    if (
      !payload.groupDiscountType &&
      parseFloat(totalDiscountedAmount) !== payload.supplierInvoiceAmount
    ) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Supplier Invoice Amount is not the same with total amount.`,
      });
    }

    const finalDiscount = !payload.groupDiscountType
      ? totalDiscount
      : payload.groupDiscountType === 'fixed'
        ? Number((Number(totalDiscount) + groupDiscountPrice).toFixed(2))
        : Number(totalDiscountedAmount * (groupDiscountPrice / 100)).toFixed(2);

    const itemSummary = {
      totalAmount,
      totalDiscount:
        payload.groupDiscountType === 'percent'
          ? Number(finalDiscount) + Number(totalDiscount)
          : finalDiscount,
      totalDiscountedAmount: !payload.groupDiscountType
        ? totalDiscountedAmount
        : payload.groupDiscountType === 'fixed'
          ? Number(
              (Number(totalDiscountedAmount) - groupDiscountPrice).toFixed(2),
            )
          : Number(Number(totalDiscountedAmount) - finalDiscount),
    };

    if (
      payload.groupDiscountType &&
      payload.groupDiscountPrice &&
      itemSummary.totalDiscountedAmount !== payload.supplierInvoiceAmount
    ) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Supplier Invoice Amount is not the same with total amount.`,
      });
    }

    const { nonRsNumber, nonRsLetter } = await this.#generateNonRsNumber(
      isDraft,
      nonRsDetails?.id,
      transaction,
    );

    if (!nonRsDetails) {
      nonRsDetails = await this.nonRequisitionRepository.create(
        {
          nonRsLetter,
          createdBy,
          status: nonRsStatus,
          [numberField]: nonRsNumber,
          groupDiscountPrice,
          ...restBody,
          ...itemSummary,
        },
        { transaction },
      );
    } else {
      const isForSubmission = !isDraft && !nonRsDetails.nonRsNumber;
      await this.nonRequisitionRepository.update(
        { id: nonRsDetails.id },
        {
          status: nonRsStatus,
          ...restBody,
          ...(isForSubmission && { nonRsLetter }),
          ...(isForSubmission && { nonRsNumber }),
          ...itemSummary,
        },
        { transaction },
      );
    }

    await this.#processNonRsItems({
      itemList,
      transaction,
      isNewRecord: !nonRsId,
      nonRsId: nonRsDetails.id,
    });

    if (!isDraft) {
      await this.#generateNonRsApprovers({
        transaction,
        nonRsId: nonRsDetails.id,
        departmentId: payload.departmentId,
        createdBy,
      });
    }

    return nonRsDetails;
  }

  async getExistingNonRs(nonRsId, options = {}) {
    const chargeToList = [
      'chargeToAssociation',
      'chargeToSupplier',
      'chargeToProject',
      'chargeToCompany',
    ];

    const chargeToInclude = chargeToList.map((key) => {
      const isCompanyOrAssociation = key === 'company' || key === 'association';

      return {
        association: key,
        as: key,
        required: false,
        attributes: [
          'id',
          'name',
          ...(isCompanyOrAssociation ? ['category'] : []),
        ],
        ...(isCompanyOrAssociation
          ? {
              where: {
                category: key,
              },
            }
          : {}),
      };
    });

    chargeToInclude.push({
      model: this.db.noteModel,
      as: 'note',
      required: false,
      attributes: ['note'],
      where: {
        model: 'non_requisition',
      },
    });

    chargeToInclude.push({
      model: this.db.noteModel,
      as: 'invoiceNotes',
      attributes: ['note'],
      required: false,
      where: {
        model: 'non_requisition_invoice',
      },
    });

    chargeToInclude.push({
      model: this.db.attachmentModel,
      as: 'attachments',
      required: false,
      where: {
        model: 'non_requisition',
      },
    });

    chargeToInclude.push({
      model: this.db.attachmentModel,
      as: 'invoiceAttachment',
      required: false,
      where: {
        model: 'non_requisition_invoice',
      },
    });

    const existingNonRs = await this.nonRequisitionRepository.getById(nonRsId, {
      include: chargeToInclude,
      ...options,
    });

    if (!existingNonRs) {
      throw this.clientErrors.NOT_FOUND({
        message: `Non RS found with ID: ${nonRsId}`,
      });
    }

    const { company, association, project, supplier, ...nonRsDetails } =
      existingNonRs;

    return nonRsDetails;
  }

  async #generateNonRsNumber(isDraft = false, nonRsId, transaction = null) {
    let whereClause;
    const { NON_RS_STATUS } = this.constants.nonRS;
    const numberField = isDraft ? 'draftNonRsNumber' : 'nonRsNumber';

    if (isDraft) {
      whereClause = {
        status: NON_RS_STATUS.DRAFT,
        draftNonRsNumber: {
          [this.db.Sequelize.Op.ne]: null,
        },
        ...(nonRsId && { id: { [this.db.Sequelize.Op.ne]: nonRsId } }),
      };
    } else {
      whereClause = {
        status: {
          [this.db.Sequelize.Op.ne]: NON_RS_STATUS.DRAFT,
        },
        nonRsNumber: {
          [this.db.Sequelize.Op.ne]: null,
        },
        ...(nonRsId && { id: { [this.db.Sequelize.Op.ne]: nonRsId } }),
      };
    }

    const lastNonRs = await this.nonRequisitionRepository.findOne({
      where: whereClause,
      order: [[numberField, 'DESC']],
      transaction,
      lock: true,
    });

    const { nextLetter, nextNumber } = this.utils.getNextNumberAndLetter(
      lastNonRs?.[numberField],
      lastNonRs?.nonRsLetter,
    );

    return {
      nonRsNumber: nextNumber,
      nonRsLetter: nextLetter,
    };
  }

  async #calculateItemListSummary({ itemList = [], isDraft = false }) {
    let totalAmount = 0;
    let totalDiscount = 0;

    if (!isDraft && itemList.length === 0) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'At least one (1) item is required for submission',
      });
    }

    itemList.forEach((item) => {
      const itemQuantity = parseFloat(item.quantity) || 0;
      const itemAmount = parseFloat(item.amount) || 0;
      const itemTotal = itemAmount * itemQuantity;

      totalAmount += itemTotal;

      if (item.discountType === 'fixed') {
        totalDiscount += parseFloat(item.discountValue) * itemQuantity;
      } else if (item.discountType === 'percent') {
        const discountAmount =
          (itemTotal * parseFloat(item.discountValue)) / 100;
        totalDiscount += discountAmount;
      }
    });

    return {
      totalAmount: totalAmount.toFixed(2),
      totalDiscount: totalDiscount.toFixed(2),
      totalDiscountedAmount: (totalAmount - totalDiscount).toFixed(2),
    };
  }

  async #processNonRsItems(payload = {}) {
    const { nonRsId, itemList, transaction } = payload;
    let existingItemList;

    if (nonRsId) {
      existingItemList = await this.nonRequisitionItemRepository.findAll({
        paginate: false,
        where: { nonRequisitionId: nonRsId },
      });
    }

    const newItemIds = itemList.map((item) => item.id);
    const existingItemIds = existingItemList?.data.map((item) => item.id) ?? [];
    const itemsToDelete = existingItemIds.filter(
      (id) => !newItemIds.includes(id),
    );

    if (itemsToDelete.length > 0) {
      this.fastify.log.info(
        `Deleting Non-RS Items: ${itemsToDelete.join(',')}`,
      );
      await this.nonRequisitionItemRepository.destroy(
        { id: { [this.db.Sequelize.Op.in]: itemsToDelete } },
        { transaction },
      );
    }

    const itemsToCreate = [];
    const itemsToUpdate = [];

    for (const item of itemList) {
      const discountedPrice =
        item.discountType === 'fixed'
          ? parseFloat(item.amount) - parseFloat(item.discountValue)
          : parseFloat(item.amount) -
            (parseFloat(item.amount) * parseFloat(item.discountValue)) / 100;
      const computedItem = { ...item, discountedPrice };

      if (item.id) {
        const existingItem = await this.nonRequisitionItemRepository.findOne({
          where: { id: item.id },
          transaction,
        });

        if (!existingItem) {
          throw this.clientErrors.NOT_FOUND({
            message: `Item with id ${item.id} does not exist`,
          });
        }

        itemsToUpdate.push(computedItem);
      } else {
        itemsToCreate.push({
          ...computedItem,
          nonRequisitionId: nonRsId,
        });
      }
    }

    if (itemsToCreate.length > 0) {
      await this.nonRequisitionItemRepository.bulkCreate(itemsToCreate, {
        transaction,
      });
    }

    for (const item of itemsToUpdate) {
      const { id, ...restItem } = item;
      await this.nonRequisitionItemRepository.update({ id }, restItem, {
        transaction,
      });
    }
  }

  async #generateNonRsApprovers(payload) {
    const { nonRsId, transaction, departmentId, createdBy } = payload;
    const { USER_TYPES } = this.constants.user;
    const { NON_RS_APPROVER_STATUS } = this.constants.nonRS;
    const approvers = await this.nonRequisitionApproverRepository.findAll({
      paginate: false,
      where: { nonRequisitionId: nonRsId },
    });

    if (approvers.total > 0) {
      const rejectedApprovers = approvers.data.filter(
        (approver) => approver.status === NON_RS_APPROVER_STATUS.REJECTED,
      );

      if (rejectedApprovers.length > 0) {
        await this.nonRequisitionApproverRepository.update(
          {
            nonRequisitionId: nonRsId,
            status: NON_RS_APPROVER_STATUS.REJECTED,
          },
          { status: NON_RS_APPROVER_STATUS.PENDING, overrideBy: null },
          { transaction },
        );
      }

      return;
    }

    const departmentApprovers = await this.departmentApprovalRepository.findAll(
      {
        order: [['level', 'ASC']],
        where: {
          [this.db.Sequelize.Op.and]: [
            { departmentId },
            { approvalTypeCode: 'RS' },
            { isOptional: false },
          ],
        },
        include: {
          model: this.db.userModel,
          as: 'approver',
          attributes: ['id', 'roleId'],
        },
      },
    );

    const user = await this.userRepository.findOne({
      where: { id: createdBy },
      include: {
        association: 'supervisor',
        attributes: ['id', 'roleId'],
      },
    });

    let currentLevel = 1;
    
    // Create supervisor approver if exists
    if (user.supervisor?.id) {
      await this.nonRequisitionApproverRepository.create(
        {
          level: currentLevel,
          userId: user.supervisor.id,
          roleId: user.supervisor.roleId,
          nonRequisitionId: nonRsId,
          status: 'pending',
        },
        { transaction },
      );
      currentLevel++;
    }

    // Create department approvers if exists
    if (departmentApprovers.data.length > 0) {
      for (const departmentApprover of departmentApprovers.data) {
        if (departmentApprover.approver?.id) {
          await this.nonRequisitionApproverRepository.create(
            {
              level: currentLevel,
              userId: departmentApprover.approver.id,
              roleId: departmentApprover.approver.roleId,
              nonRequisitionId: nonRsId,
              status: 'pending',
            },
            { transaction },
          );
          currentLevel++;
        }
      }
    }
  }

  async #addAdhocApprover(payload = {}) {
    const { existingNonRs, toAddApproverId, currentApprover, transaction } =
      payload;

    const { USER_STATUS } = this.constants.user;
    const { NON_RS_APPROVER_STATUS } = this.constants.nonRS;

    const approverToAdd = await this.userRepository.findOne({
      where: {
        id: toAddApproverId,
        status: USER_STATUS.ACTIVE,
      },
      attributes: ['id', 'roleId', 'status'],
    });

    if (!approverToAdd) {
      throw this.clientErrors.NOT_FOUND({
        message: 'Currently selected approver not found',
      });
    }

    const { id: nonRSId } = existingNonRs;
    const { id: approverToAddId, roleId: approverToAddRoleId } = approverToAdd;

    if (!currentApprover) {
      throw this.clientErrors.FORBIDDEN({
        message: 'Only existing approvers can add adhoc approvers',
      });
    }

    if (currentApprover.isAdhoc) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Adhoc approvers cannot add other adhoc approvers',
      });
    }

    const isExistingApprover =
      await this.nonRequisitionApproverRepository.findOne({
        where: {
          nonRequisitionId: nonRSId,
          userId: approverToAddId,
        },
        transaction,
      });

    if (isExistingApprover) {
      return;
    }

    const existingAdhoc = await this.nonRequisitionApproverRepository.findOne({
      where: {
        nonRequisitionId: nonRSId,
        level: currentApprover.level,
        isAdhoc: true,
      },
      transaction,
    });

    if (existingAdhoc) {
      await this.nonRequisitionApproverRepository.update(
        { id: existingAdhoc.id },
        { userId: approverToAddId },
        { transaction },
      );

      return;
    }

    await this.nonRequisitionApproverRepository.create(
      {
        nonRequisitionId: nonRSId,
        userId: approverToAddId,
        level: currentApprover.level,
        isAdhoc: true,
        roleId: approverToAddRoleId,
      },
      { transaction },
    );
  }

  async approveRejectNonRS(payload = {}) {
    const {
      existingNonRs,
      approverId,
      transaction,
      userFromToken,
      hasApproved,
      toAddApproverId,
      itemList,
    } = payload;
    const { NON_RS_APPROVER_STATUS, NON_RS_STATUS } = this.constants.nonRS;
    const { NOTIFICATION_TYPES, NOTIFICATION_DETAILS } =
      this.constants.notification;
    const approveText = NON_RS_APPROVER_STATUS.APPROVED ? 'approve' : 'reject';

    const isReadyForApproval =
      existingNonRs.status !== NON_RS_STATUS.FOR_APPROVAL;

    if (isReadyForApproval) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Non Requistion Request is not yet for approval`,
      });
    }

    const approvers = await this.nonRequisitionApproverRepository.getApprovers(
      existingNonRs.id,
    );

    const userApproverRecords = approvers.data.filter((approverRecord) => {
      const isApprover =
        approverRecord.userId === approverId ||
        approverRecord.altApproverId === approverId;

      return isApprover;
    });

    if (userApproverRecords.length === 0) {
      throw this.clientErrors.FORBIDDEN({
        message: `You are not authorized to ${approveText} this non-requisition`,
      });
    }

    const pendingApprovals = userApproverRecords
      .filter((record) => record.status === NON_RS_APPROVER_STATUS.PENDING)
      .sort((a, b) => {
        if (a.level !== b.level) return a.level - b.level;
        if (a.isAdhoc !== b.isAdhoc) return a.isAdhoc ? 1 : -1;
        return 0;
      });

    const currentApprover =
      pendingApprovals.length > 0 ? pendingApprovals[0] : null;

    if (!currentApprover) {
      throw this.clientErrors.BAD_REQUEST({
        message: `You have no pending approvals for this non-requisition`,
      });
    }

    if (hasApproved && toAddApproverId) {
      await this.#addAdhocApprover({
        existingNonRs,
        currentApprover,
        transaction,
        toAddApproverId,
      });
    }

    const currentApproverStatus = hasApproved
      ? NON_RS_APPROVER_STATUS.APPROVED
      : NON_RS_APPROVER_STATUS.REJECTED;

    if (currentApprover.status === currentApproverStatus) {
      return;
    }

    await this.approverService.overrideApprover({
      model: 'nonRequisition',
      modelId: existingNonRs.id,
      approverId,
      status: currentApproverStatus,
      transaction,
      requisitionId: existingNonRs.requisitionId,
    });

    await this.nonRequisitionApproverRepository.update(
      { id: currentApprover.id },
      { status: currentApproverStatus },
      { transaction },
    );

    if (hasApproved && itemList?.length) {
      await this.#processNonRsItems({
        itemList,
        transaction,
        nonRsId: existingNonRs.id,
      });

      await this.notificationService.sendNotification({
        transaction,
        senderId: userFromToken.id,
        type: NOTIFICATION_TYPES.NON_RS,
        title: NOTIFICATION_DETAILS.UPDATE_NON_RS.title,
        message: NOTIFICATION_DETAILS.UPDATE_NON_RS.message(
          existingNonRs.nonRsLetter + existingNonRs.nonRsNumber,
        ),
        recipientUserIds: [existingNonRs.createdBy],
        metaData: {
          approvedBy: userFromToken.id,
          nonRsId: existingNonRs.id,
        },
      });
    }

    await this.#createNonRSHistory({
      approverId: currentApprover.userId,
      status: currentApproverStatus,
      nonRequisitionId: existingNonRs.id,
      transaction,
    });

    const everyApproverApproved =
      await this.nonRequisitionApproverRepository.findAll({
        where: {
          nonRequisitionId: existingNonRs.id,
          status: {
            [this.db.Sequelize.Op.ne]: NON_RS_APPROVER_STATUS.APPROVED,
          },
        },
        paginate: false,
        transaction,
      });

    if (!hasApproved) {
      await this.nonRequisitionRepository.update(
        { id: existingNonRs.id },
        { status: NON_RS_STATUS.REJECTED },
        { transaction },
      );
    } else if (!everyApproverApproved.total) {
      await this.nonRequisitionRepository.update(
        { id: existingNonRs.id },
        { status: NON_RS_STATUS.CLOSED },
        { transaction },
      );

      if (existingNonRs.category !== 'association') {
        await this.sendApprovedNonRSToAccounting(existingNonRs, userFromToken);
      }
    }
  }

  async removeAdhocApprover(payload = {}) {
    const { id, primaryApproverId } = payload;
    const { NON_RS_APPROVER_STATUS } = this.constants.nonRS;

    const approvers =
      await this.nonRequisitionApproverRepository.getApprovers(id);

    const primaryApprover = approvers.data.find(
      (approver) => approver.userId === primaryApproverId && !approver.isAdhoc,
    );

    if (!primaryApprover) {
      throw this.clientErrors.FORBIDDEN({
        message: 'You are not authorized to remove adhoc approvers',
      });
    }

    const adhocApprover = approvers.data.find(
      (approver) =>
        approver.isAdhoc && approver.level === primaryApprover.level,
    );

    if (!adhocApprover) {
      throw this.clientErrors.NOT_FOUND({
        message: `No adhoc approver found for level ${primaryApprover.level}`,
      });
    }

    await this.nonRequisitionApproverRepository.destroy({
      id: adhocApprover.id,
    });
  }

  async #createNonRSHistory(payload = {}) {
    const { approverId, nonRequisitionId, status, transaction } = payload;
    const { createNonRsHistorySchema } = this.entities.nonRequisition;

    const parsedHistoryPayload = createNonRsHistorySchema.parse({
      approverId,
      nonRequisitionId,
      status,
    });

    await this.nonRequisitionHistoryRepository.create({
      ...parsedHistoryPayload,
      transaction,
    });
  }

  async addAdhocApprover(payload = {}) {
    const { nonRsId, approver, creatorId, transaction } = payload;
    const { NON_RS_APPROVER_STATUS } = this.constants.nonRS;

    const approvers = await this.nonRequisitionApproverRepository.findAll({
      paginate: false,
      where: {
        userId: creatorId,
        nonRequisitionId: nonRsId,
      },
      order: [
        ['level', 'ASC'],
        ['isAdhoc', 'ASC'],
      ],
    });

    if (!approvers.data.length) {
      throw this.clientErrors.FORBIDDEN({
        message: 'Only existing approvers can add adhoc approvers',
      });
    }

    const creatorApprover = approvers.data[0];

    if (creatorApprover.isAdhoc) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Adhoc approvers cannot add other adhoc approvers',
      });
    }

    const isExistingApprover =
      await this.nonRequisitionApproverRepository.findOne({
        where: {
          nonRequisitionId: nonRsId,
          userId: approver.id,
        },
      });

    if (isExistingApprover) {
      return;
    }

    const existingAdhoc = await this.nonRequisitionApproverRepository.findOne({
      where: {
        nonRequisitionId: nonRsId,
        level: creatorApprover.level,
        isAdhoc: true,
      },
    });

    if (existingAdhoc) {
      await this.nonRequisitionApproverRepository.update(
        { id: existingAdhoc.id },
        { userId: approver.id },
        { transaction },
      );

      return;
    }

    await this.nonRequisitionApproverRepository.create(
      {
        nonRequisitionId: nonRsId,
        userId: approver.id,
        level: creatorApprover.level,
        isAdhoc: true,
        roleId: approver.role.id,
      },
      { transaction },
    );
  }

  async generateNonRSPdf(nonRsId) {
    const nonRs = await this.nonRequisitionRepository.getById(nonRsId, {
      include: [
        {
          association: 'company',
          as: 'company',
          attributes: ['id', 'tin', 'name'],
        },
        {
          association: 'project',
          as: 'project',
          attributes: ['id', 'name'],
        },
      ],
    });
    const items =
      await this.nonRequisitionItemRepository.getAllItemListByNonRsId(nonRsId, {
        paginate: false,
      });

    if (!items || !items.total) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'No items found for this Non-RS',
      });
    }

    const templatesPath = path.join(path.resolve(), 'src', 'templates', 'pdf');
    const singleTemplatePath = path.join(
      templatesPath,
      'single-non-rs-pr-template.pdf',
    );
    const twoPageTemplatePath = path.join(
      templatesPath,
      'multiple-non-rs-pr-template-page-1.pdf',
    );
    const lastPageTemplatePath = path.join(
      templatesPath,
      'multiple-non-rs-pr-template-page-final.pdf',
    );

    const resultDoc = await PDFDocument.create();
    const itemCount = items.total;

    const minLength = 9;
    const secondPageMinLength = 16;

    if (itemCount <= minLength) {
      const singleTemplate = await PDFDocument.load(
        fs.readFileSync(singleTemplatePath),
      );

      await this.fillSingleTemplate(singleTemplate, nonRs, items.data);
      await this.#fillItemCountDetails(singleTemplate, 1, minLength, itemCount);
      await this.#fillTableAmount(
        singleTemplate,
        nonRs.totalDiscountedAmount,
        minLength + 1,
      );

      singleTemplate.getForm().flatten();
      const [singlePage] = await resultDoc.copyPages(singleTemplate, [0]);
      resultDoc.addPage(singlePage);
    } else if (itemCount <= 24) {
      const twoPageTemplate = await PDFDocument.load(
        fs.readFileSync(twoPageTemplatePath),
      );
      const lastPageTemplate = await PDFDocument.load(
        fs.readFileSync(lastPageTemplatePath),
      );

      await this.fillTwoPageTemplate(
        twoPageTemplate,
        nonRs,
        items.data.slice(0, secondPageMinLength),
        0,
      );

      await this.#fillPageNumber(twoPageTemplate, 1, 2);

      await this.#fillItemCountDetails(
        twoPageTemplate,
        1,
        Math.min(itemCount, secondPageMinLength),
        itemCount,
      );

      await this.fillLastPageTemplate(
        lastPageTemplate,
        nonRs,
        items.data.slice(secondPageMinLength),
        secondPageMinLength,
      );
      await this.#fillItemCountDetails(
        lastPageTemplate,
        Math.min(items.total, secondPageMinLength),
        itemCount,
        itemCount,
      );

      await this.#fillTableAmount(
        lastPageTemplate,
        nonRs.totalDiscountedAmount,
        10,
      );
      await this.#fillPageNumber(lastPageTemplate, 2, 2);

      twoPageTemplate.getForm().flatten();
      lastPageTemplate.getForm().flatten();
      const [singlePage] = await resultDoc.copyPages(twoPageTemplate, [0]);
      const [lastPage] = await resultDoc.copyPages(lastPageTemplate, [0]);

      resultDoc.addPage(singlePage);
      resultDoc.addPage(lastPage);
    } else {
      const twoPageTemplate = await PDFDocument.load(
        fs.readFileSync(twoPageTemplatePath),
      );
      const lastPageTemplate = await PDFDocument.load(
        fs.readFileSync(lastPageTemplatePath),
      );

      const tableItems = items.data;
      const chunkSize = 16;
      const fullChunks = Math.floor(tableItems.length / chunkSize);
      const totalPages = fullChunks + 1;

      for (let i = 0; i < fullChunks; i++) {
        const offset = i * chunkSize;
        const currentPage = i + 1;

        const chunk = tableItems.slice(offset, offset + chunkSize);
        await this.fillTwoPageTemplate(twoPageTemplate, nonRs, chunk, offset);
        await this.#fillItemCountDetails(
          twoPageTemplate,
          offset + 1,
          offset + chunkSize,
          itemCount,
        );
        await this.#fillPageNumber(twoPageTemplate, currentPage, totalPages);

        twoPageTemplate.getForm().flatten();
        const [twoPage] = await resultDoc.copyPages(twoPageTemplate, [0]);
        resultDoc.addPage(twoPage);
      }

      const remainingOffset = fullChunks * chunkSize;
      const remaining = tableItems.slice(remainingOffset);
      await this.fillLastPageTemplate(
        lastPageTemplate,
        nonRs,
        remaining,
        remainingOffset,
      );

      await this.#fillItemCountDetails(
        lastPageTemplate,
        remainingOffset,
        itemCount,
        itemCount,
      );

      await this.#fillTableAmount(
        lastPageTemplate,
        nonRs.totalDiscountedAmount,
        remainingOffset + 1,
      );
      await this.#fillPageNumber(lastPageTemplate, totalPages, totalPages);

      lastPageTemplate.getForm().flatten();
      const [lastPage] = await resultDoc.copyPages(lastPageTemplate, [0]);
      resultDoc.addPage(lastPage);
    }

    const pdfBytes = await resultDoc.save();

    const dateNow = new Date();
    const currentDate = this.utils.formatDateYYYYMMDD(dateNow);
    const currentTime = this.utils.convertToHHMMSS(dateNow);
    const fileName = `NRS-${currentDate}-${currentTime}`;

    const outputPath = `./upload/non_requistion_downloads/${fileName}.pdf`;

    if (!fs.existsSync(path.dirname(outputPath))) {
      fs.mkdirSync(path.dirname(outputPath), { recursive: true });
    }

    fs.writeFileSync(outputPath, pdfBytes);
    return outputPath;
  }

  async #fillHeaderTemplate(pdfDoc, nonRs) {
    const form = pdfDoc.getForm();
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const fontBold = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

    const projectCompany = form.getTextField('project-company');
    projectCompany.setText(nonRs.project?.name || nonRs.company?.name || '');
    projectCompany.updateAppearances(fontBold);
    projectCompany.setAlignment(1);

    const nonRsNumber = form.getTextField('rsNumber');
    nonRsNumber.setText(`NRS-${nonRs.nonRsLetter}${nonRs.nonRsNumber}`);
    nonRsNumber.updateAppearances(fontBold);

    const datePrepared = form.getTextField('datePrepared');
    datePrepared.setText(this.utils.convertDateToDDMMMYYYY(new Date()));
    datePrepared.updateAppearances(font);

    const dateNeeded = form.getTextField('dateNeeded');
    dateNeeded.setText(this.utils.convertDateToDDMMMYYYY(nonRs.invoiceDate));
    dateNeeded.updateAppearances(font);
  }

  async #fillRequestDetails(pdfDoc, nonRs) {
    const form = pdfDoc.getForm();
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const fontBold = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

    const payableTo = form.getTextField('to');
    payableTo.setText(nonRs.payableTo || '');
    payableTo.updateAppearances(font);

    const tin = form.getTextField('tin');
    tin.setText(nonRs.company?.tin || '');
    tin.updateAppearances(font);

    const balance = form.getTextField('balance');
    balance.setText(
      `P${this.utils.formatCurrency(nonRs.totalDiscountedAmount, 2, 'decimal')}`,
    );
    balance.updateAppearances(fontBold);
  }

  async #fillItemCountDetails(pdfDoc, start = 0, end = 0, total = 0) {
    const form = pdfDoc.getForm();
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const fontBold = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

    const firstItemCount = form.getTextField('firstItemCount');
    firstItemCount.setText(start.toString());
    firstItemCount.updateAppearances(fontBold);

    const lastItemCount = form.getTextField('lastItemCount');
    lastItemCount.setText(end.toString());
    lastItemCount.updateAppearances(fontBold);

    const totalItems = form.getTextField('total');
    totalItems.setText(total.toString());
    totalItems.updateAppearances(font);
  }

  async #fillItemsTable(pdfDoc, items, offset = 0) {
    const form = pdfDoc.getForm();
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);

    items?.forEach((item, index) => {
      const itemIndex = index + 1;
      const itemNum = form.getTextField(`itemNum${itemIndex}`);
      const itemName = form.getTextField(`itemName${itemIndex}`);
      const qty = form.getTextField(`qty${itemIndex}`);
      const unit = form.getTextField(`unit${itemIndex}`);
      const unitPrice = form.getTextField(`unitPrice${itemIndex}`);
      const discount = form.getTextField(`discount${itemIndex}`);
      const price = form.getTextField(`price${itemIndex}`);

      itemNum.setText(`${itemIndex + offset}`);
      itemName.setText(item.name);
      qty.setText(item.quantity.toString());
      unit.setText(item.unit);
      unitPrice.setText(
        `P${this.utils.formatCurrency(item.amount / item.quantity, 2, 'decimal')}`,
      );
      discount.setText(
        `P${this.utils.formatCurrency(item.discountValue, 2, 'decimal')}`,
      );
      price.setText(`P${this.utils.formatCurrency(item.amount, 2, 'decimal')}`);

      itemNum.updateAppearances(font);
      itemName.updateAppearances(font);
      qty.updateAppearances(font);
      unit.updateAppearances(font);
      unitPrice.updateAppearances(font);
      unitPrice.setAlignment(2);
      discount.updateAppearances(font);
      price.updateAppearances(font);
      price.setAlignment(2);
    });
  }

  async #fillTableAmount(pdfDoc, amount, offset = 0) {
    const form = pdfDoc.getForm();
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);

    const price = form.getTextField(`price${offset}`);

    price.setText(`P${this.utils.formatCurrency(amount, 2, 'decimal')}`);
    price.updateAppearances(font);
    price.setAlignment(2);
  }

  async #fillPageNumber(pdfDoc, start = 1, end = 1) {
    const form = pdfDoc.getForm();
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const fontBold = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

    const currentPage = form.getTextField('currentPage');
    currentPage.setText(start.toString());
    currentPage.updateAppearances(fontBold);

    const totalPage = form.getTextField('totalPage');
    totalPage.setText(end.toString());
    totalPage.updateAppearances(font);
  }

  async fillSingleTemplate(pdfDoc, nonRs, items) {
    await this.#fillHeaderTemplate(pdfDoc, nonRs);
    await this.#fillRequestDetails(pdfDoc, nonRs);
    await this.#fillItemsTable(pdfDoc, items);
    await this.#fillPageNumber(pdfDoc, 1, 1);
  }

  async fillTwoPageTemplate(pdfDoc, nonRs, items, offset) {
    await this.#fillHeaderTemplate(pdfDoc, nonRs);
    await this.#fillRequestDetails(pdfDoc, nonRs);
    await this.#fillItemsTable(pdfDoc, items, offset);
  }

  async fillLastPageTemplate(pdfDoc, nonRs, items, offset) {
    const form = pdfDoc.getForm();
    const fontBoldItalic = await pdfDoc.embedFont(
      StandardFonts.HelveticaBoldOblique,
    );

    await this.#fillHeaderTemplate(pdfDoc, nonRs);
    await this.#fillItemsTable(pdfDoc, items, offset);

    const totalAmountWords = form.getTextField('totalAmount');
    totalAmountWords.setText(
      this.utils.amountInWordsPHP(nonRs.totalDiscountedAmount),
    );
    totalAmountWords.updateAppearances(fontBoldItalic);
  }

  async sendApprovedNonRSToAccounting(nonRequisition, userFromToken) {
    try {
      this.fastify.log.info(
        `Sending approved non-requisition to accounting system: ${nonRequisition.id}`,
      );

      // Generate the payload for accounting
      const formattedData = await this.generateNonRSAccountingPayload(
        nonRequisition,
        userFromToken,
      );

      // Send to accounting system
      const response = await this.citylandApiService.post(
        '/payment_processing/voucher_request/',
        formattedData,
      );

      this.fastify.log.info(
        `Successfully sent non-requisition to accounting system. Response: ${JSON.stringify(response)}`,
      );
      return response;
    } catch (error) {
      this.fastify.log.error(
        `Failed to send non-requisition to accounting: ${error.message}`,
      );
      throw error;
    }
  }

  async generateNonRSAccountingPayload(nonRequisition, userFromToken) {
    try {
      // Get non-requisition items
      const items =
        await this.nonRequisitionItemRepository.getAllItemListByNonRsId(
          nonRequisition.id,
          { paginate: false },
        );

      // Group items by account code
      const itemsByAccountCode = {};

      items.data.forEach((item) => {
        const accountCode = '**************';

        if (!itemsByAccountCode[accountCode]) {
          itemsByAccountCode[accountCode] = {
            account_code: accountCode,
            amount: 0,
            items: [],
          };
        }

        // Calculate item amount based on quantity and unit price
        const itemAmount =
          Number(item.quantity || 0) * Number(item.discountedPrice || 0);

        // Check if this item already exists in the items array
        const existingItemIndex = itemsByAccountCode[
          accountCode
        ].items.findIndex(
          (existingItem) =>
            existingItem.description === item.description &&
            existingItem.unit === item.unit,
        );

        if (existingItemIndex >= 0) {
          // Update existing item
          const existingItem =
            itemsByAccountCode[accountCode].items[existingItemIndex];
          existingItem.quantity += Number(item.quantity || 0);
          existingItem.amount += itemAmount;
        } else {
          // Add new item
          itemsByAccountCode[accountCode].items.push({
            description: item.name.substring(0, 30),
            quantity: Number(item.quantity || 0),
            unit: item.unit.substring(0, 4),
            amount: itemAmount,
          });
        }

        // Update total amount for this account code
        itemsByAccountCode[accountCode].amount += itemAmount;
      });

      // Format NR number
      const nrNumber = `NR${nonRequisition.nonRsLetter}${nonRequisition.nonRsNumber}`;

      // Get date and time
      const now = new Date();
      const datePrepared = now.toISOString().split('T')[0];
      const timePrepared = now.toTimeString().split(' ')[0];

      // Round amounts to 2 decimal places
      Object.values(itemsByAccountCode).forEach((accountLine) => {
        accountLine.amount = Number(accountLine.amount.toFixed(2));
        accountLine.items.forEach((item) => {
          item.amount = Number(item.amount.toFixed(2));
        });
      });

      // Prepare supplier data
      const supplier = await this.supplierRepository.findOne({
        where: { id: nonRequisition.supplierId },
        attributes: ['id', 'name', 'tin', 'payCode'],
      });

      const supplierData = {
        name: supplier?.name.trim().substring(0, 35) || 'N/A',
        tin: supplier?.tin.trim().substring(0, 20) || 'N/A',
        code: supplier?.payCode.trim().substring(0, 4) || 'N/A',
      };

      // Get project and company codes
      const project = await this.db.projectModel.findOne({
        where: { id: nonRequisition.projectId },
        attributes: ['code'],
      });

      const company = await this.db.companyModel.findOne({
        where: { id: nonRequisition.companyId },
        attributes: ['code'],
      });

      const projectCode = project?.code.substring(0,3) || 'N/A';
      const companyCode = company?.code || 'N/A';

      return {
        voucher_request: {
          prs_username: userFromToken.lastName.substring(0, 10),
          voucher_request_no: nrNumber,
          branch_code: '1',
          company_code: companyCode,
          project_code: projectCode,
          date_prepared: datePrepared,
          time_prepared: timePrepared,
          supplier: supplierData,
          total_amount: Number(nonRequisition.totalDiscountedAmount || 0),
          lines: Object.values(itemsByAccountCode),
        },
      };
    } catch (error) {
      this.fastify.log.error(
        `Failed to generate non-RS accounting payload: ${error.message}`,
      );
      throw error;
    }
  }
}

module.exports = NonRequisitionService;
