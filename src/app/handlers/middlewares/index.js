const authenticate = require('./authenticate');
const authorize = require('./authorize');
const verifyOTPToken = require('./verifyOTPToken');
const verifyPassToken = require('./verifyPassToken');
const verifyRefreshToken = require('./verifyRefreshToken');
const uploadFile = require('./uploadFile');
const slowUploadFile = require('./slowUploadFile');
const uploadLimit = require('./uploadLimit');
const auditLogs = require('./auditLogs');
const transactionLogs = require('./transactionLogs');

module.exports = {
  authenticate,
  authorize,
  verifyOTPToken,
  verifyPassToken,
  verifyRefreshToken,
  uploadFile,
  slowUploadFile,
  uploadLimit,
  auditLogs,
  transactionLogs,
};
