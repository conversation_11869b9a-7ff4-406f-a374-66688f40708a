class rsPaymentRequest {
  constructor(container) {
    const {
      userService,
      noteService,
      rsPaymentRequestApproverRepository,
      rsPaymentRequestRepository,
      constants,
      attachmentService,
      commentService,
      db,
      utils,
      entities,
      rsPaymentRequestService,
      fastify,
      leaveRepository,
      clientErrors,
      invoiceReportService,
      templateService,
      gatePassService,
      purchaseOrderService,
    } = container;

    this.httpClient = new utils.HTTPClient({
      endpoint: `${process.env.CITYLAND_ACCOUNTING_URL}`,
      logger: fastify.log,
    });
    this.userService = userService;
    this.noteService = noteService;
    this.rsPaymentRequestApproverRepository =
      rsPaymentRequestApproverRepository;
    this.rsPaymentRequestRepository = rsPaymentRequestRepository;
    this.constants = constants;
    this.commentService = commentService;
    this.attachmentService = attachmentService;
    this.fastify = fastify;
    this.db = db;
    this.utils = utils;
    this.entities = entities;
    this.rsPaymentRequestService = rsPaymentRequestService;
    this.leaveRepository = leaveRepository;
    this.clientErrors = clientErrors;
    this.invoiceReportService = invoiceReportService;
    this.templateService = templateService;
    this.gatePassService = gatePassService;
    this.purchaseOrderService = purchaseOrderService;
  }

  async addPRComment(request, reply) {
    const { notes } = request.body;
    const { userFromToken } = request;
    const { MODELS } = this.constants.attachment;
    const paymentRequestId = parseInt(request.params.id);
    const existingPaymentRequest =
      await this.rsPaymentRequestService.getExistingPR(paymentRequestId);

    const createdComment = await this.commentService.createComment({
      comment: notes,
      model: MODELS.RS_PAYMENT_REQUEST,
      userId: userFromToken.id,
      modelId: existingPaymentRequest.id,
    });

    return reply.status(200).send({
      message: 'Note added successfully',
      note: createdComment,
    });
  }

  async createRsPaymentRequest(request, reply) {
    const { userFromToken, body } = request;
    const { attachments, ...details } = { ...body };

    const parsedDetails = this.utils.parseDomain(
      this.entities.rsPaymentRequest.createRsPaymentRequestSchema,
      details,
    );
    let paymentRequest;

    // Get PO details including supplier, delivery address, terms, etc.
    const { termsData } =
      await this.rsPaymentRequestService.getPurchaseOrderDetails({
        id: parsedDetails.purchaseOrderId,
        employeeId: parsedDetails?.termsData?.employeeId,
        userFromToken,
      });

    // Create payment request
    paymentRequest = await this.rsPaymentRequestService.createRsPaymentRequest({
      ...parsedDetails,
      invoiceIds: body.invoiceIds,
      termsData,
      transaction: request.transaction,
      userFromToken,
    });

    return reply.status(201).send({
      message: `Payment Request ${
        parsedDetails.isDraft ? 'draft created' : 'created'
      } successfully`,
      paymentRequest,
    });
  }

  async getPurchaseOrderDetails(request, reply) {
    const { purchaseOrderId } = request.params;

    const purchaseOrderDetails =
      await this.rsPaymentRequestService.getPurchaseOrderDetails({
        ...request,
        id: purchaseOrderId,
      });
    return reply.status(200).send({
      message: `Purchase Order details`,
      result: purchaseOrderDetails,
    });
  }

  async getPaymentRequest(request, reply) {
    const paymentRequestId = parseInt(request.params.id);
    const paymentRequest =
      await this.rsPaymentRequestService.getPaymentRequestById(
        paymentRequestId,
      );

    return reply.status(200).send(paymentRequest);
  }

  async getPRItems(request, reply) {
    const { search, sortBy, type, ...queries } = request.query;
    const { prSortSchema } = this.entities.rsPaymentRequest;
    const parsedSortBy = prSortSchema.parse(sortBy);
    const purchaseOrderId = parseInt(request.params.id);
    const prItems = await this.rsPaymentRequestService.getPRItems(
      purchaseOrderId,
      search,
      parsedSortBy,
      queries,
      type,
    );

    return reply.status(200).send(prItems);
  }

  async getPRComments(request, reply) {
    const paymentRequestId = parseInt(request.params.id);
    const { MODELS } = this.constants.attachment;
    const { APPROVERS, USER_TYPES } = this.constants.user;
    const { commentDateTo, commentDateFrom } = request.query;
    const existingPaymentRequest =
      await this.rsPaymentRequestService.getExistingPR(paymentRequestId);

    const comments = await this.commentService.getAllComments({
      model: [MODELS.RS_PAYMENT_REQUEST, MODELS.RS_PAYMENT_REQUEST_REJECT],
      modelId: existingPaymentRequest.id,
      filters: request.query,
      commentDateTo,
      commentDateFrom,
    });

    const approverRoleList = [
      ...Object.values(APPROVERS),
      USER_TYPES.MANAGEMENT,
    ];

    /* TODO: To Refactor - once comment table supports role type and note type */
    const groupedComments = comments.data.reduce((acc, comment) => {
      const date = new Date(comment.createdAt).toISOString().split('T')[0];

      if (!acc[date]) {
        acc[date] = [];
      }

      acc[date].push({
        ...comment,
        noteType:
          comment.model === MODELS.RS_PAYMENT_REQUEST ? 'Note' : 'Disapproval',
        roleType: approverRoleList.includes(comment.userComment.role.name)
          ? 'Approver'
          : 'Requestor',
      });

      return acc;
    }, {});

    const mappedComments = {
      ...comments,
      data: Object.entries(groupedComments).map(([date, comments]) => ({
        date,
        comments,
      })),
    };

    return reply.status(200).send(mappedComments);
  }

  async getPRAttachments(request, reply) {
    const paymentRequestId = parseInt(request.params.id);
    const { MODELS } = this.constants.attachment;
    const { attachmentDateFrom, attachmentDateTo, ...restFilters } =
      request.query;
    const existingPaymentRequest =
      await this.rsPaymentRequestService.getExistingPR(paymentRequestId);

    const attachments = await this.attachmentService.getAttachments({
      model: MODELS.RS_PAYMENT_REQUEST,
      attachmentDateTo,
      attachmentDateFrom,
      modelId: existingPaymentRequest.id,
      filters: restFilters,
    });

    return reply.status(200).send(attachments);
  }

  async getPRApprovers(request, reply) {
    const paymentRequestId = parseInt(request.params.id);
    const existingPaymentRequest =
      await this.rsPaymentRequestService.getExistingPR(paymentRequestId);

    const approvers =
      await this.rsPaymentRequestApproverRepository.getPRApprovers(
        existingPaymentRequest.id,
      );

    return reply.status(200).send(approvers);
  }

  async submitPaymentRequest(request, reply) {
    const result = await this.rsPaymentRequestService.submit(request);

    return reply.status(200).send({
      message: `Successfully submitted Payment Request with ID of ${result.paymentRequest.id}`,
      result,
    });
  }

  async approvePurchaseRequest(request, reply) {
    const { userFromToken, transaction } = request;
    const { approveReason } = request.body;
    const { id } = request.params;
    const paymentRequestId = parseInt(id);
    const { MODELS, USER_TYPES, COMMENT_TYPES } = this.constants.note;

    const existingPaymentRequest =
      await this.rsPaymentRequestService.getExistingPR(paymentRequestId);

    const allApproved =
      await this.rsPaymentRequestService.approvePurchaseRequest({
        transaction,
        existingPaymentRequest,
        userFromToken: request.userFromToken,
      });

    if (allApproved) {
      // const accountingPayload =
      //   await this.rsPaymentRequestService.generateAccountingPayload(
      //     existingPaymentRequest,
      //     userFromToken
      //   );

      // this.fastify.log.info('Sending info to cityland accounting api');
      // await this.httpClient.post({
      //   path: '/submit',
      //   body: accountingPayload,
      // });

      await this.gatePassService.generateGatePass({
        paymentRequestId: existingPaymentRequest.id,
        userFromToken: request.userFromToken,
        transaction,
        fromAllApproved: true,
      });

      await this.purchaseOrderService.closePurchaseOrder(
        parseInt(existingPaymentRequest?.purchaseOrderId),
        transaction,
      );
    }

    if (approveReason) {
      await this.noteService.createNote(
        {
          model: MODELS.RS_PAYMENT_REQUEST,
          modelId: existingPaymentRequest.id,
          userName: userFromToken.fullNameUser,
          userType: USER_TYPES.APPROVER,
          commentType: COMMENT_TYPES.APPROVAL,
          note: approveReason,
        },
        {
          transaction,
        },
      );
    }

    return reply.status(200).send({
      message: `Payment request approved successfully`,
    });
  }

  async getPOLists(request, reply) {
    const { requisitionId } = request.query;
    const result = await this.rsPaymentRequestService.getPOlists(requisitionId);
    return reply.status(200).send({
      message: 'successfully retrieve po lists',
      result,
    });
  }

  async addAdhocApprover(request, reply) {
    const { userFromToken, transaction } = request;
    const { approverId } = request.body;
    const { USER_TYPES } = this.constants.user;
    const paymentRequestId = parseInt(request.params.id);

    const existingPaymentRequest =
      await this.rsPaymentRequestService.getExistingPR(paymentRequestId);

    /* Ensure that user exist with valid role at first index */
    const approverRoleList = [
      ...Object.values(this.constants.user.APPROVERS),
      USER_TYPES.PURCHASING_STAFF,
    ];

    const users = await this.userService.validateMultipleUsers([approverId], {
      roleNames: approverRoleList,
    });

    await this.rsPaymentRequestService.addAdhocApprover({
      transaction,
      approver: users[0],
      creatorId: userFromToken.id,
      paymentRequestId: existingPaymentRequest.id,
      requisitionId: existingPaymentRequest.requisitionId,
      fullName: userFromToken.fullNameUser,
    });

    return reply.status(200).send({
      message: 'Payment Request approver updated successfully',
    });
  }

  async rejectPaymentRequest(request, reply) {
    const { userFromToken, transaction } = request;
    const { rejectReason } = request.body;
    const paymentRequestId = parseInt(request.params.id);
    const { MODELS, USER_TYPES, COMMENT_TYPES } = this.constants.note;

    const existingPaymentRequest =
      await this.rsPaymentRequestService.getExistingPR(paymentRequestId);

    await this.rsPaymentRequestService.rejectPaymentRequest({
      transaction,
      existingPaymentRequest,
      approverId: userFromToken.id,
      userFromToken,
    });

    await this.noteService.createNote({
      model: MODELS.RS_PAYMENT_REQUEST,
      modelId: existingPaymentRequest.id,
      userName: userFromToken.fullNameUser,
      userType: USER_TYPES.APPROVER,
      commentType: COMMENT_TYPES.DISAPPROVAL,
      note: rejectReason,
    });

    return reply.status(200).send({
      message: 'Payment rejected successfully',
    });
  }

  async getPaymentRequestsFromRequisitionId(request, reply) {
    const { query, params } = request;

    const paymentRequests =
      await this.rsPaymentRequestService.getPaymentRequestsFromRequisitionId(
        params.requisitionId,
        {
          search: query.search,
          page: query.page,
          limit: query.limit,
          sortBy: query.sortBy,
        },
      );

    return reply.status(200).send({ ...paymentRequests });
  }

  async removeAdhocApprover(request, reply) {
    const { id } = request.params;
    const { userFromToken } = request;

    await this.rsPaymentRequestService.removeAdhocApprover({
      id,
      primaryApproverId: userFromToken.id,
    });

    return reply.status(200).send({
      message: 'Non requisition approver successfully removed',
    });
  }

  async resubmitRejectedPaymentRequest(request, reply) {
    const { userFromToken, transaction, body } = request;
    const paymentRequestId = parseInt(request.params.id);
    const existingPaymentRequest =
      await this.rsPaymentRequestService.getExistingPR(paymentRequestId);

    await this.rsPaymentRequestService.resubmitRejectedPaymentRequest({
      transaction,
      existingPaymentRequest,
      userFromToken,
      body,
    });

    return reply.status(200).send({
      message: `Successfully resubmitted payment request with ID: ${paymentRequestId}`,
    });
  }

  async getInvoicesByPurchaseOrderId(request, reply) {
    const { id } = request.params;

    const invoices =
      await this.rsPaymentRequestService.getInvoicesByPurchaseOrderId(id);

    return reply.status(200).send({
      message: 'Invoices retrieved successfully',
      invoices,
    });
  }

  async getInvoicesByPaymentRequestId(request, reply) {
    const { id } = request.params;
    const { query } = request;

    const invoiceReports =
      await this.invoiceReportService.getInvoiceReportsByPaymentRequestId(
        id,
        query,
      );

    return reply.status(200).send({
      message: 'Invoice reports retrieved successfully',
      invoiceReports,
    });
  }

  async getPaymentRequestMonitoring(request, reply) {
    const { purchaseOrderId } = request.params;

    const monitoringData =
      await this.rsPaymentRequestService.getPaymentRequestMonitoring(
        parseInt(purchaseOrderId),
      );

    return reply.status(200).send({
      message: 'Payment request monitoring data retrieved successfully',
      data: monitoringData,
    });
  }

  async generateRsPaymentRequestPdf(request, reply) {
    const paymentRequestId = parseInt(request.params.id);

    try {
      const result =
        await this.templateService.createRsPaymentRequestPdf(paymentRequestId);
      this.fastify.log.info(
        `PDF generated, sending response with ${result.pdfBytes.length} bytes`,
      );

      return reply
        .header('Content-Type', 'application/pdf')
        .header(
          'Content-Disposition',
          `attachment; filename="${result.fileName}"`,
        )
        .send(result.pdfBytes);
    } catch (error) {
      this.fastify.log.error(
        `Error in requisitionPDF controller: ${error.message}`,
      );
      throw error;
    }
  }

  async generateGatePassPdf(request, reply) {
    const paymentRequestId = parseInt(request.params.id);

    try {
      const result = await this.gatePassService.generateGatePass({
        paymentRequestId,
        userFromToken: request.userFromToken,
      });

      if (!result) {
        return reply.send({
          message: `The payment request with id ${paymentRequestId} is not ready to have a gate pass or it is not a type of transfer of materials`,
        });
      }

      return reply.send({
        message: `Generated PDF successfully`,
      });
    } catch (error) {
      this.fastify.log.error(
        `Error in requisitionPDF controller: ${error.message}`,
      );
      throw error;
    }
  }

  async testAccountingPayload(request, reply) {
    const paymentRequestId = parseInt(request.params.id);
    const { userFromToken } = request;

    try {
      const existingPaymentRequest =
        await this.rsPaymentRequestService.getExistingPR(paymentRequestId);

      const accountingPayload =
        await this.rsPaymentRequestService.generateAccountingPayload(
          existingPaymentRequest,
          userFromToken,
        );

      return reply.status(200).send({
        message: 'Accounting payload generated successfully',
        payload: accountingPayload,
      });
    } catch (error) {
      this.fastify.log.error(
        `Error generating accounting payload: ${error.message}`,
      );
      throw error;
    }
  }
}

module.exports = rsPaymentRequest;
