const BaseRepository = require('./baseRepository');

class PurchaseOrderItemRepository extends BaseRepository {
  constructor({ db, steelbarsRepository, purchaseOrderRepository }) {
    super(db.purchaseOrderItemModel);
    this.db = db;
    this.Sequelize = db.Sequelize;
    this.steelbarsRepository = steelbarsRepository;
    this.purchaseOrderRepository = purchaseOrderRepository;
  }

  async getPOItemsById(payload) {
    const {
      purchaseOrderId,
      search,
      paginate,
      page,
      limit,
      sortBy = '{"itemName": "ASC"}',
      transaction = null,
    } = payload;

    // Convert the parsedSortBy object into an array format for Sequelize ordering
    const order = Object.entries(JSON.parse(sortBy)).map(([key, value]) => [
      key,
      value.toUpperCase(),
    ]);

    const whereConditions = {
      purchaseOrderId,
      [this.Sequelize.Op.and]: [
        // Ensure we have either OFM or non-OFM items
        {
          [this.Sequelize.Op.or]: [
            {
              '$requisitionItemList.item.id$': { [this.Sequelize.Op.ne]: null },
            },
            {
              '$requisitionItemList.nonOfmItem.id$': {
                [this.Sequelize.Op.ne]: null,
              },
            },
          ],
        },
        // Add search condition if provided
        ...(search
          ? [
              {
                [this.Sequelize.Op.or]: [
                  // Search OFM items
                  {
                    [this.Sequelize.Op.and]: [
                      {
                        '$requisitionItemList.item_type$': {
                          [this.Sequelize.Op.in]: ['ofm', 'ofm-tom'],
                        },
                      },
                      {
                        '$requisitionItemList.item.itm_des$': {
                          [this.Sequelize.Op.iLike]: `%${search}%`,
                        },
                      },
                    ],
                  },
                  // Search non-OFM items
                  {
                    [this.Sequelize.Op.and]: [
                      {
                        '$requisitionItemList.item_type$': {
                          [this.Sequelize.Op.in]: ['non-ofm', 'non-ofm-tom'],
                        },
                      },
                      {
                        '$requisitionItemList.nonOfmItem.item_name$': {
                          [this.Sequelize.Op.iLike]: `%${search}%`,
                        },
                      },
                    ],
                  },
                ],
              },
            ]
          : []),
      ],
    };

    const poItems = await this.findAll(
      {
        where: whereConditions,
        attributes: [
          'id',
          'quantityPurchased', // TODO: delete this after integration with FE
          [
            this.Sequelize.col('purchase_order_items.quantity_purchased'),
            'approvedQuantity',
          ],
          'canvassItemId',
          'requisitionItemListId',
          [
            this.Sequelize.col('canvassItemSupplier.quantity'),
            'quantityRequested',
          ],
          [
            this.Sequelize.col('canvassItemSupplier.unit_price'),
            'originalPrice',
          ], // TODO: delete this after integration with FE
          [this.Sequelize.col('canvassItemSupplier.unit_price'), 'unitPrice'],
          [
            // TODO: delete this after integration with FE
            this.Sequelize.literal(
              'CASE ' +
                'WHEN "canvassItemSupplier"."discount_type" = \'fixed\' THEN ' +
                '"canvassItemSupplier"."unit_price" - "canvassItemSupplier"."discount_value" ' +
                'WHEN "canvassItemSupplier"."discount_type" = \'percent\' THEN ' +
                '("canvassItemSupplier"."unit_price" * (1 - "canvassItemSupplier"."discount_value" / 100)) ' +
                'ELSE "canvassItemSupplier"."unit_price" - "canvassItemSupplier"."discount_value" ' +
                'END',
            ),
            'canvassedPrice',
          ],
          [
            this.Sequelize.literal(
              'CASE ' +
                'WHEN "canvassItemSupplier"."discount_type" = \'fixed\' THEN ' +
                '"canvassItemSupplier"."unit_price" - "canvassItemSupplier"."discount_value" ' +
                'WHEN "canvassItemSupplier"."discount_type" = \'percent\' THEN ' +
                '("canvassItemSupplier"."unit_price" * (1 - "canvassItemSupplier"."discount_value" / 100)) ' +
                'ELSE "canvassItemSupplier"."unit_price" - "canvassItemSupplier"."discount_value" ' +
                'END',
            ),
            'approvedPrice',
          ],
          [
            this.Sequelize.col('canvassItemSupplier.discount_type'),
            'discountType',
          ],
          [
            this.Sequelize.col('canvassItemSupplier.discount_value'),
            'discountValue',
          ],
          [
            this.Sequelize.fn(
              'COALESCE',
              this.Sequelize.col('requisitionItemList.item.acct_cd'),
              this.Sequelize.col('requisitionItemList.nonOfmItem.acct_cd'),
            ),
            'accountCode',
          ],
          [
            this.Sequelize.fn(
              'COALESCE',
              this.Sequelize.col('requisitionItemList.item.unit'),
              this.Sequelize.col('requisitionItemList.nonOfmItem.unit'),
            ),
            'unit',
          ],
          [
            this.Sequelize.fn(
              'COALESCE',
              this.Sequelize.col('requisitionItemList.item.itm_des'),
              this.Sequelize.col('requisitionItemList.nonOfmItem.item_name'),
            ),
            'itemName',
          ],
          [
            this.Sequelize.col('requisitionItemList.item.steelbars.weight'),
            'weight',
          ],
          [
            this.Sequelize.col('requisitionItemList.item.steelbars.grade'),
            'grade',
          ],
          [
            this.Sequelize.col('requisitionItemList.item.steelbars.length'),
            'length',
          ],
          [
            this.Sequelize.col('requisitionItemList.item.steelbars.diameter'),
            'diameter',
          ],
        ],
        include: [
          {
            association: 'canvassItemSupplier',
            attributes: [
              'quantity',
              'unit_price',
              'discount_value',
              'supplier_name',
            ],
            required: false,
          },
          {
            association: 'requisitionItemList',
            attributes: ['notes', 'itemType', 'quantity'],
            required: true,
            include: [
              {
                association: 'item',
                required: false,
                attributes: [
                  'id',
                  'itmDes',
                  'acctCd',
                  'remainingGfq',
                  'unit',
                  'isSteelbars',
                ],
                include: [
                  {
                    association: 'steelbars',
                    required: false,
                    attributes: ['grade', 'diameter', 'length', 'weight'],
                  },
                ],
              },
              {
                association: 'nonOfmItem',
                required: false,
                attributes: ['id', 'itemName', 'acctCd', 'unit'],
              },
            ],
          },
        ],
        paginate,
        page,
        limit,
        order,
      },
      {
        transaction,
      },
    );

    if (poItems.data) {
      poItems.data = await Promise.all(
        poItems.data.map(async (item) => {
          const processedItem = {
            id: item.id,
            itemId:
              item.requisitionItemList?.item?.id ||
              item.requisitionItemList?.nonOfmItem?.id,
            quantityPurchased: item.quantityPurchased, // TODO: retain for now to prevent error from FE, will delete this after integration with FE
            approvedQuantity: item.approvedQuantity,
            quantityRequested: item.requisitionItemList?.quantity, // TODO: retain for now to prevent error from FE, will delete this after integration with FE
            requestedQuantity: item.quantityRequested,
            originalPrice: item.originalPrice, // TODO: retain for now to prevent error from FE, will delete this after integration with FE
            unitPrice: item.unitPrice, // TODO: retain for now to prevent error from FE, will delete this after integration with FE
            canvassedPrice: item.canvassedPrice,
            approvedPrice: item.approvedPrice,
            accountCode: item.accountCode,
            unit: item.unit,
            notes: item.requisitionItemList.notes,
            itemType: item.requisitionItemList.itemType,
            discountType: item.discountType,
            discountValue: item.discountValue,
            canvassItemId: item.canvassItemId,
            requisitionItemListId: item.requisitionItemListId,
            totalPrice:
              parseFloat(item.canvassedPrice) *
              parseFloat(item.approvedQuantity),
          };

          switch (item.requisitionItemList.itemType) {
            case 'ofm':
            case 'ofm-tom':
              processedItem.remainingGfq =
                item.requisitionItemList?.item?.remainingGfq;
              processedItem.itemName = item.requisitionItemList?.item?.itmDes;
              processedItem.isSteelbars =
                item.requisitionItemList?.item?.isSteelbars; //perhaps useless given that there's an itemType already.
              if (processedItem.accountCode && processedItem.isSteelbars) {
                processedItem.weight =
                  item.requisitionItemList?.item?.steelbars?.weight ?? null;
                processedItem.grade =
                  item.requisitionItemList?.item?.steelbars?.grade ?? null;
                processedItem.length =
                  item.requisitionItemList?.item?.steelbars?.length ?? null;
                processedItem.diameter =
                  item.requisitionItemList?.item?.steelbars?.diameter ?? null;
              }
              break;
            case 'non-ofm':
            case 'non-ofm-tom':
              processedItem.itemName =
                item.requisitionItemList?.nonOfmItem?.itemName;
              break;
          }

          return processedItem;
        }),
      );
    }

    return poItems;
  }

  async getPOItemsSummary(payload) {
    const { purchaseOrderId } = payload;

    const sqlQuery = `
      SELECT
        MAX(purchase_order_items.id) AS id,
        SUM("canvass_item_suppliers"."unit_price" * "purchase_order_items"."quantity_purchased") AS amount,
        SUM(CASE
            WHEN "canvass_item_suppliers"."discount_type" = 'fixed' THEN ("canvass_item_suppliers"."discount_value") * "purchase_order_items"."quantity_purchased"
            WHEN "canvass_item_suppliers"."discount_type" = 'percent' THEN ("canvass_item_suppliers"."unit_price" * ("canvass_item_suppliers"."discount_value" / 100)) * "purchase_order_items"."quantity_purchased"
            ELSE 0
        END)
        AS discount,
       SUM("canvass_item_suppliers"."unit_price" * "purchase_order_items"."quantity_purchased") - SUM(CASE
          WHEN "canvass_item_suppliers"."discount_type" = 'fixed' THEN ("canvass_item_suppliers"."discount_value") * "purchase_order_items"."quantity_purchased"
          WHEN "canvass_item_suppliers"."discount_type" = 'percent' THEN ("canvass_item_suppliers"."unit_price" * ("canvass_item_suppliers"."discount_value" / 100)) * "purchase_order_items"."quantity_purchased"
          ELSE 0
      END)
      AS total_amount
      FROM purchase_order_items
      LEFT JOIN canvass_item_suppliers
        ON canvass_item_suppliers.id = purchase_order_items.canvass_item_supplier_id
      WHERE purchase_order_items.purchase_order_id = :purchaseOrderId
      GROUP BY purchase_order_items.purchase_order_id;
    `;

    const [data] = await this.db.sequelize.query(sqlQuery, {
      replacements: { purchaseOrderId },
      type: this.db.Sequelize.QueryTypes.SELECT,
    });

    const {
      addedDiscount,
      isAddedDiscountFixedAmount,
      isAddedDiscountPercentage,
    } = await this.purchaseOrderRepository.findOne({
      where: {
        id: purchaseOrderId,
      },
    });

    let canvassDiscount = parseFloat(data.discount).toFixed(2);
    let totalDiscount = parseFloat(data.discount).toFixed(2);
    let totalAmount = parseFloat(data.total_amount).toFixed(2);

    if (addedDiscount > 0 && isAddedDiscountFixedAmount) {
      const purchaseOrderFixedAmountDiscount =
        parseFloat(addedDiscount).toFixed(2);

      totalDiscount =
        parseFloat(purchaseOrderFixedAmountDiscount) +
        parseFloat(canvassDiscount);

      totalAmount = (
        parseFloat(data.total_amount) -
        parseFloat(purchaseOrderFixedAmountDiscount)
      ).toFixed(2);
    }

    if (addedDiscount > 0 && isAddedDiscountPercentage) {
      const purchaseOrderPercentageDiscount =
        (addedDiscount / 100) * data.total_amount;

      totalDiscount =
        parseFloat(purchaseOrderPercentageDiscount) +
        parseFloat(canvassDiscount);

      totalAmount = (
        parseFloat(data.total_amount) -
        parseFloat(purchaseOrderPercentageDiscount)
      ).toFixed(2);
    }

    return (
      { ...data, discount: totalDiscount, totalAmount } || {
        id: null,
        amount: 0,
        discount: 0,
        totalAmount: 0,
      }
    );
  }

  async getItemsForPaymentRequest(payload = {}) {
    const {
      page,
      limit,
      search,
      paginate,
      purchaseOrderId,
      type,
      sort = { itemName: 'asc' },
    } = payload;

    const isOfm = ['ofm', 'ofm-tom'].includes(type);

    let itemNameSearch = {};
    if (search) {
      itemNameSearch = {
        [this.Sequelize.Op.or]: [
          this.Sequelize.where(
            this.Sequelize.col('requisitionItemList.item.itm_des'),
            { [this.Sequelize.Op.iLike]: `%${search}%` },
          ),
          this.Sequelize.where(
            this.Sequelize.col('requisitionItemList.nonOfmItem.item_name'),
            { [this.Sequelize.Op.iLike]: `%${search}%` },
          ),
        ],
      };
    }

    const orderBy = Object.entries(sort).map(([field, direction]) => {
      switch (field) {
        case 'accountCode':
          return [
            isOfm
              ? this.Sequelize.col('requisitionItemList.item.acct_cd')
              : this.Sequelize.col('requisitionItemList.nonOfmItem.acct_cd'),
            direction.toUpperCase(),
          ];
        case 'itemName':
          return [
            isOfm
              ? this.Sequelize.col('requisitionItemList.item.itm_des')
              : this.Sequelize.col('requisitionItemList.nonOfmItem.item_name'),
            direction.toUpperCase(),
          ];
        case 'qtyDelivered':
          return [
            this.Sequelize.col('deliveryReceiptItems.qty_delivered'),
            direction.toUpperCase(),
          ];
        case 'amount':
          return [
            this.Sequelize.literal(
              '"canvassItemSupplier"."unit_price" * "deliveryReceiptItems"."qty_delivered"',
            ),
            direction.toUpperCase(),
          ];
      }
    });

    const poItems = await this.findAll({
      page,
      limit,
      paginate,
      where: { purchaseOrderId, ...itemNameSearch },
      attributes: [
        [
          isOfm
            ? this.Sequelize.col('requisitionItemList.item.acct_cd')
            : this.Sequelize.col('requisitionItemList.nonOfmItem.acct_cd'),
          'accountCode',
        ],
        [
          isOfm
            ? this.Sequelize.col('requisitionItemList.item.itm_des')
            : this.Sequelize.col('requisitionItemList.nonOfmItem.item_name'),
          'itemName',
        ],
        [
          isOfm
            ? this.Sequelize.col('requisitionItemList.item.unit')
            : this.Sequelize.col('requisitionItemList.nonOfmItem.unit'),
          'unit',
        ],
        'quantityPurchased',
        ...(isOfm
          ? [
              [
                this.Sequelize.col('requisitionItemList.item.remaining_gfq'),
                'remainingGfq',
              ],
            ]
          : []),
        [
          this.Sequelize.col('deliveryReceiptItems.qty_delivered'),
          'qtyDelivered',
        ],
        [
          this.Sequelize.literal(
            'CASE ' +
              'WHEN "canvassItemSupplier"."discount_type" = \'fixed\' THEN ' +
              '("canvassItemSupplier"."unit_price" - "canvassItemSupplier"."discount_value") * "deliveryReceiptItems"."qty_delivered" ' +
              'WHEN "canvassItemSupplier"."discount_type" = \'percent\' THEN ' +
              '("canvassItemSupplier"."unit_price" * (1 - "canvassItemSupplier"."discount_value" / 100)) * "deliveryReceiptItems"."qty_delivered" ' +
              'ELSE "canvassItemSupplier"."unit_price" * "deliveryReceiptItems"."qty_delivered" ' +
              'END',
          ),
          'amount',
        ],
        'quantityPurchased',
        ...(isOfm
          ? [
              [
                this.Sequelize.col('requisitionItemList.item.is_steelbars'),
                'isSteelbars',
              ],
            ]
          : []),
        ...(isOfm
          ? [
              [
                this.Sequelize.literal(`
                  CASE
                    WHEN "requisitionItemList->item"."is_steelbars" = true THEN
                      (
                        SELECT jsonb_build_object(
                          'id', sb.id,
                          'grade', sb.grade,
                          'diameter', sb.diameter,
                          'length', sb.length,
                          'weight', sb.weight,
                          'ofmAcctcd', sb.ofm_acctcd
                        )
                        FROM "steelbars" sb
                        WHERE sb.ofm_acctcd = "requisitionItemList->item"."acct_cd"
                      )
                    ELSE NULL
                  END
                `),
                'steelbars',
              ],
            ]
          : []),
        'requisitionItemListId',
      ],
      include: [
        {
          association: 'canvassItemSupplier',
          as: 'canvassItemSupplier',
        },
        {
          association: 'requisitionItemList',
          attributes: ['notes', 'itemType'],
          required: false,
          include: [
            isOfm
              ? {
                  association: 'item',
                  required: false,
                  attributes: [],
                  include: [
                    {
                      association: 'steelbars',
                      as: 'steelbars',
                      required: false,
                      attributes: [],
                    },
                  ],
                }
              : {
                  association: 'nonOfmItem',
                  required: false,
                  attributes: [],
                  where: {
                    ...itemNameSearch.nonOfmItem,
                  },
                },
          ],
        },
        {
          association: 'deliveryReceiptItems',
          as: 'deliveryReceiptItems',
        },
      ],
      // order: orderBy.length > 0 ? orderBy : undefined,
    });

    return poItems;
  }

  async getPOItemsForReceiving(payload = {}) {
    const { purchaseOrderId, sortBy = '{"itemName": "asc"}' } = payload;

    const purchaseOrder = await this.purchaseOrderRepository.findOne({
      attributes: [],
      where: { id: purchaseOrderId },
      include: [{ association: 'requisition', attributes: ['type'] }],
    });

    const isOfm = ['ofm', 'ofm-tom'].includes(purchaseOrder.requisition.type);
    const sort = typeof sortBy === 'string' ? JSON.parse(sortBy) : sortBy;

    // Build order by clause
    const orderBy = Object.entries(sort).map(([field, direction]) => {
      if (field === 'itemName') {
        return [
          isOfm
            ? this.Sequelize.col('requisitionItemList.item.itm_des')
            : this.Sequelize.col('requisitionItemList.nonOfmItem.item_name'),
          direction.toUpperCase(),
        ];
      } else {
        return ['id', 'ASC'];
      }
    });

    const poItems = await this.findAll({
      paginate: false,
      where: {
        purchaseOrderId,
        [this.Sequelize.Op.and]: [
          this.Sequelize.literal(`
            "purchase_order_items"."quantity_purchased" > (
              SELECT COALESCE(SUM("delivery_receipt_items"."qty_delivered"), 0)
              FROM "delivery_receipt_items"
              JOIN "delivery_receipts" ON "delivery_receipt_items"."dr_id" = "delivery_receipts"."id"
              WHERE "delivery_receipt_items"."po_item_id" = "purchase_order_items"."id"
              AND "delivery_receipts"."is_draft" = false
            )
          `),
        ],
      },
      attributes: [
        'id', // purchase order item id
        [
          isOfm
            ? this.Sequelize.col('requisitionItemList.item.id')
            : this.Sequelize.col('requisitionItemList.nonOfmItem.id'),
          'itemId',
        ],
        [
          isOfm
            ? this.Sequelize.col('requisitionItemList.item.acct_cd')
            : this.Sequelize.col('requisitionItemList.nonOfmItem.acct_cd'),
          'accountCode',
        ],
        [
          isOfm
            ? this.Sequelize.col('requisitionItemList.item.itm_des')
            : this.Sequelize.col('requisitionItemList.nonOfmItem.item_name'),
          'itemName',
        ],
        'quantityPurchased',
        [
          this.Sequelize.fn(
            'COALESCE',
            this.Sequelize.cast(
              this.Sequelize.fn(
                'SUM',
                this.Sequelize.col('deliveryReceiptItems.qty_returned'),
              ),
              'FLOAT',
            ),
            0,
          ),
          'totalReturned',
        ],
        [
          this.Sequelize.fn(
            'COALESCE',
            this.Sequelize.cast(
              this.Sequelize.fn(
                'SUM',
                this.Sequelize.col('deliveryReceiptItems.qty_delivered'),
              ),
              'FLOAT',
            ),
            0,
          ),
          'totalDelivered',
        ],
        [
          this.Sequelize.literal(
            'CAST("purchase_order_items"."quantity_purchased" - COALESCE(SUM(CASE WHEN "deliveryReceiptItems->deliveryReceipt"."is_draft" = false THEN "deliveryReceiptItems"."qty_delivered" ELSE 0 END), 0) AS FLOAT)',
          ),
          'remainingQtyForDelivery',
        ],
        [
          isOfm
            ? this.Sequelize.col('requisitionItemList.item.is_steelbars')
            : this.Sequelize.literal('false'),
          'isSteelbar',
        ],
        [
          isOfm
            ? this.Sequelize.col('requisitionItemList.item.unit')
            : this.Sequelize.col('requisitionItemList.nonOfmItem.unit'),
          'unit',
        ],
      ],
      include: [
        {
          association: 'requisitionItemList',
          attributes: [],
          required: true,
          include: [
            isOfm
              ? {
                  association: 'item',
                  required: false,
                  attributes: [],
                }
              : {
                  association: 'nonOfmItem',
                  required: false,
                  attributes: [],
                },
          ],
        },
        {
          association: 'deliveryReceiptItems',
          attributes: [],
          required: false,
          include: [
            {
              association: 'deliveryReceipt',
              attributes: [],
              required: false,
            },
          ],
        },
      ],
      group: [
        'purchase_order_items.id',
        'purchase_order_items.quantity_purchased',
        ...(isOfm
          ? [
              'requisitionItemList.item.id',
              'requisitionItemList.item.acct_cd',
              'requisitionItemList.item.itm_des',
              'requisitionItemList.item.is_steelbars',
              'requisitionItemList.item.unit',
            ]
          : [
              'requisitionItemList.nonOfmItem.id',
              'requisitionItemList.nonOfmItem.acct_cd',
              'requisitionItemList.nonOfmItem.item_name',
              'requisitionItemList.nonOfmItem.unit',
            ]),
        'requisitionItemList.id',
      ],
      order: orderBy,
      subQuery: false,
    });

    return poItems;
  }
}

module.exports = PurchaseOrderItemRepository;
