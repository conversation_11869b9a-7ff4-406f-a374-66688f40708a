const BaseRepository = require('./baseRepository');

class DeliveryReceiptItemRepository extends BaseRepository {
  constructor({ db, clientErrors }) {
    super(db.deliveryReceiptItemModel);
    this.clientErrors = clientErrors;
  }

  async getAllDeliveryReceiptItemsByPOId(purchaseOrderId, options = {}) {
    const { where = {} } = options;
    return this.findAll({
      where: {
        poId: purchaseOrderId,
        ...where,
      },
      paginate: false,
      ...options,
    });
  }

  async getItem(id) {
    const result = await this.findOne({
      where: { id },
    });

    if (!result) {
      throw this.clientErrors.NOT_FOUND({
        message: `Delivery receipt item with id of ${id} not found`,
      });
    }

    return result;
  }

  async updateItem(id, data, options) {
    const result = await this.update(
      {
        id,
      },
      data,
      options,
    );

    if (!result.length || result[0] === 0) {
      throw this.clientErrors.NOT_FOUND({
        message: `Delivery receipt item with id of ${id} not found`,
      });
    }

    return result;
  }
  async syncDeliveryReceiptItems(
    deliveryReceipt,
    requestBodyItems,
    { transaction, userId },
  ) {
    const existingItems = await this.tableName.findAll({
      where: { drId: deliveryReceipt.id },
      transaction,
    });

    const itemsToDelete = existingItems.filter((item) => {
      return item.poId !== deliveryReceipt.poId;
    });

    await Promise.all(
      itemsToDelete.map((item) => item.destroy({ transaction })),
    );

    const itemsToCreate = requestBodyItems.filter((item) => {
      return Number(item.poId) === deliveryReceipt.poId;
    });

    await Promise.all(
      itemsToCreate.map((item) =>
        this.tableName.create(
          { ...item, drId: deliveryReceipt.id },
          { transaction, userId },
        ),
      ),
    );
  }
}

module.exports = DeliveryReceiptItemRepository;
