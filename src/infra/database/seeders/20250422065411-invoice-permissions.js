'use strict';
const { USER_TYPES } = require('../../../domain/constants/userConstants');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const dateNow = new Date();
    const existingPermissions = await queryInterface.sequelize.query(
      `SELECT id, module, action FROM permissions WHERE module = :module`,
      {
        replacements: { module: 'invoice' },
        type: queryInterface.sequelize.QueryTypes.SELECT,
      },
    );

    const invoicePermissionIds = existingPermissions.map(permission => permission.id);
    const existingRolePermissions = await queryInterface.sequelize.query(
      `SELECT * FROM role_permissions WHERE permission_id IN (${invoicePermissionIds})`,
      {
        type: queryInterface.sequelize.QueryTypes.SELECT,
      },
    );

    console.log({ existingPermissions, existingRolePermissions });

    if (!existingPermissions.length || !existingRolePermissions.length) {
      const permissions = [
        {
          module: 'invoice',
          action: 'create',
          created_at: dateNow,
          updated_at: dateNow,
        },
        {
          module: 'invoice',
          action: 'view',
          created_at: dateNow,
          updated_at: dateNow,
        },
        {
          module: 'invoice',
          action: 'get',
          created_at: dateNow,
          updated_at: dateNow,
        },
        {
          module: 'invoice',
          action: 'update',
          created_at: dateNow,
          updated_at: dateNow,
        },
        {
          module: 'invoice',
          action: 'delete',
          created_at: dateNow,
          updated_at: dateNow,
        },
        {
          module: 'invoice',
          action: 'approval',
          created_at: dateNow,
          updated_at: dateNow,
        },
      ];

      await queryInterface.sequelize.transaction(async (transaction) => {
        await queryInterface.bulkInsert('permissions', permissions, {
          transaction,
          ignoreDuplicates: true,
        });

        const roles = await queryInterface.sequelize.query(
          `SELECT id FROM roles WHERE name NOT IN (:name)`,
          {
            replacements: {
              name: USER_TYPES.ROOT_USER,
            },
            type: queryInterface.sequelize.QueryTypes.SELECT,
            transaction,
          },
        );

        const roleIds = roles.map((role) => role.id);

        const invoicePermissions = await queryInterface.sequelize.query(
          `SELECT id FROM permissions WHERE module = :module`,
          {
            replacements: { module: 'invoice' },
            type: queryInterface.sequelize.QueryTypes.SELECT,
            transaction,
          },
        );

        const permissionIds = invoicePermissions.map(
          (permission) => permission.id,
        );

        const rolePermissions = roleIds.flatMap((roleId) =>
          permissionIds.map((permissionId) => ({
            role_id: roleId,
            permission_id: permissionId,
            created_at: dateNow,
            updated_at: dateNow,
          })),
        );

        await queryInterface.bulkInsert('role_permissions', rolePermissions, {
          transaction,
        });
      });
    }
  },

  async down(queryInterface, Sequelize) {
    const existingPermissions = await queryInterface.sequelize.query(
      `SELECT id FROM permissions WHERE module = :module`,
      {
        replacements: { module: 'invoice' },
        type: queryInterface.sequelize.QueryTypes.SELECT,
      },
    );

    if (existingPermissions.length) {
      await queryInterface.sequelize.transaction(async (transaction) => {
        await queryInterface.bulkDelete(
          'permissions',
          {
            module: 'invoice',
          },
          { transaction },
        );

        const roles = await queryInterface.sequelize.query(
          `SELECT id FROM roles WHERE name NOT IN (:name)`,
          {
            replacements: {
              name: USER_TYPES.ROOT_USER,
            },
            type: queryInterface.sequelize.QueryTypes.SELECT,
            transaction,
          },
        );

        const roleIds = roles.map((role) => role.id);

        const permissionIds = existingPermissions.map(
          (permission) => permission.id,
        );

        await queryInterface.bulkDelete(
          'role_permissions',
          {
            role_id: { [Sequelize.Op.in]: roleIds },
            permission_id: { [Sequelize.Op.in]: permissionIds },
          },
          { transaction },
        );
      });
    }
  },
};
