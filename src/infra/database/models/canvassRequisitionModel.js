const {
  CANVASS_STATUS,
} = require('../../../domain/constants/canvassConstants');
const { item, canvass } = require('../../../domain/entities');

module.exports = (sequelize, Sequelize) => {
  const CanvassRequisitionModel = sequelize.define(
    'canvass_requisitions',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      requisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'requisitions',
          key: 'id',
        },
        validate: {
          isInt: true,
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      csNumber: {
        type: Sequelize.STRING(8),
        allowNull: true,
        field: 'cs_number',
      },
      csLetter: {
        type: Sequelize.STRING(2),
        allowNull: false,
        field: 'cs_letter',
      },
      draftCsNumber: {
        type: Sequelize.STRING(8),
        allowNull: true,
        field: 'draft_cs_number',
      },
      status: {
        allowNull: false,
        type: Sequelize.STRING(255),
        defaultValue: CANVASS_STATUS.DRAFT,
      },
      // Cancellation tracking fields for force close functionality
      cancelledAt: {
        type: Sequelize.DATE,
        allowNull: true,
        field: 'cancelled_at',
      },
      cancelledBy: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'cancelled_by',
        references: {
          model: 'users',
          key: 'id',
        },
      },
      cancellationReason: {
        type: Sequelize.STRING(100),
        allowNull: true,
        field: 'cancellation_reason',
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  CanvassRequisitionModel.associate = (models) => {
    CanvassRequisitionModel.hasMany(models.canvassItemModel, {
      foreignKey: 'canvassRequisitionId',
      as: 'canvassItems',
    });

    CanvassRequisitionModel.belongsTo(models.requisitionModel, {
      foreignKey: 'requisitionId',
      as: 'requisition',
    });

    CanvassRequisitionModel.hasMany(models.canvassApproverModel, {
      foreignKey: 'canvassRequisitionId',
      as: 'canvassApprovers',
    });
  };

  CanvassRequisitionModel.afterUpdate(async (canvassRequisition, options) => {
    try {
      if (canvassRequisition.changed('status')) {
        const requisition = await sequelize.model('requisitions').findOne({
          attributes: ['companyCode'],
          where: {
            id: canvassRequisition.requisitionId,
          },
        });

        const items = await sequelize.model('canvass_items').findAll({
          where: {
            canvassRequisitionId: canvassRequisition.id,
          },
          include: [
            {
              model: sequelize.model('requisition_item_lists'),
              as: 'requisitionItem',
            },
            {
              model: sequelize.model('canvass_item_suppliers'),
              as: 'suppliers',
              where: {
                isSelected: true,
              },
            },
          ],
        });

        const data = items.map(async (item) => {
          // fetch item name
          let itemName = '';
          const itemType = item.requisitionItem.itemType;

          if (itemType === 'ofm' || itemType === 'ofm-tom') {
            const ofmItemResult = await sequelize.model('items').findOne({
              where: {
                id: item.requisitionItem.itemId,
              },
            });
            itemName = ofmItemResult.itmDes;
          } else {
            const nonOfmItemResult = await sequelize
              .model('non_ofm_items')
              .findOne({
                where: {
                  id: item.requisitionItem.itemId,
                },
              });
            itemName = nonOfmItemResult.itemName;
          }

          return item.suppliers.map(async (supplier) => {
            const { unitPrice, discountType, discountValue } = supplier;

            // fetch supplier name
            let supplierName = '';
            if (supplier.supplierType === 'company') {
              const companyResult = await sequelize.model('companies').findOne({
                attributes: ['name'],
                where: {
                  id: supplier.supplierId,
                },
              });
              supplierName = companyResult.name;
            } else if (supplier.supplierType === 'project') {
              const projectResult = await sequelize.model('projects').findOne({
                attributes: ['name'],
                where: {
                  id: supplier.supplierId,
                },
              });
              supplierName = projectResult.name;
            } else {
              const supplierResult = await sequelize
                .model('suppliers')
                .findOne({
                  attributes: ['name'],
                  where: {
                    id: supplier.supplierId,
                  },
                });
              supplierName = supplierResult.name;
            }

            await sequelize.model('requisition_canvass_histories').create({
              canvassNumber: `CS-${requisition.companyCode}${canvassRequisition.csLetter}${canvassRequisition.csNumber}`,
              requisitionId: canvassRequisition.requisitionId,
              requisitionItemListId: item.requisitionItem.id,
              supplierId: supplier.supplierId,
              canvassRequisitionId: canvassRequisition.id,
              supplier: supplierName,
              item: itemName,
              price: unitPrice,
              discount: discountType === 'fixed' 
                ? discountValue 
                : unitPrice * (discountValue / 100),
              canvassDate: new Date(),
              status: canvassRequisition.status === CANVASS_STATUS.APPROVED
                ? CANVASS_STATUS.APPROVED
                : CANVASS_STATUS.FOR_APPROVAL,
            }, { transaction: options.transaction });
          });
        });

        await Promise.all(data);
      }
    } catch (error) {
      console.log(
        'HOOK_ERROR - canvassRequisitionModel - afterUpdate: ',
        error.stack,
      );
    }
  });
  return CanvassRequisitionModel;
};
