module.exports = (sequelize, Sequelize) => {
  const OfmItemListModel = sequelize.define(
    'ofm_item_lists',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      listName: {
        type: Sequelize.STRING(100),
        allowNull: false,
        field: 'list_name',
      },
      companyCode: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'company_code',
      },
      projectCode: {
        type: Sequelize.STRING(20),
        allowNull: true,
        field: 'project_code',
      },
      tradeCode: {
        type: Sequelize.INTEGER,
        allowNull: true,
        field: 'trade_code',
      },
    },
    {
      timestamps: true,
      underscored: true,
      indexes: [
        {
          unique: true,
          fields: ['list_name', 'created_at'],
          namme: 'ofm_item_lists_list_name_key_with_time_1755847570984_index',
        },
      ],
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  OfmItemListModel.associate = (models) => {
    OfmItemListModel.belongsTo(models.companyModel, {
      foreignKey: 'company_code',
      targetKey: 'code',
      as: 'company',
    });

    OfmItemListModel.belongsTo(models.projectModel, {
      foreignKey: 'project_code',
      targetKey: 'code',
      as: 'project',
    });

    OfmItemListModel.belongsTo(models.tradeModel, {
      foreignKey: 'trade_code',
      targetKey: 'tradeCode',
      as: 'trade',
    });

    OfmItemListModel.hasMany(models.ofmListItemModel, {
      foreignKey: 'ofm_list_id',
      as: 'listItems',
    });
  };

  return OfmItemListModel;
};
