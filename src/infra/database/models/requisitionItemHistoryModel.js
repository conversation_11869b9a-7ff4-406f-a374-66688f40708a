module.exports = (sequelize, Sequelize) => {
  const RequisitionItemHistoryModel = sequelize.define(
    'requisition_item_histories',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      requisitionId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'requisitions',
          key: 'id',
        },
        field: 'requisition_id',
      },
      item: {
        type: Sequelize.STRING,
        allowNull: false,
        field: 'item',
      },
      quantityRequested: {
        type: Sequelize.FLOAT,
        allowNull: false,
        field: 'quantity_requested',
      },
      quantityOrdered: {
        type: Sequelize.FLOAT,
        allowNull: false,
        field: 'quantity_ordered',
      },
      quantityDelivered: {
        type: Sequelize.FLOAT,
        allowNull: false,
        field: 'quantity_delivered',
      },
      lastUpdatedBy: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id',
        },
        field: 'last_updated_by',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'created_at',
        defaultValue: Sequelize.fn('now'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        field: 'updated_at',
        defaultValue: Sequelize.fn('now'),
      },
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  RequisitionItemHistoryModel.associate = (models) => {
    RequisitionItemHistoryModel.belongsTo(models.userModel, {
      foreignKey: 'lastUpdatedBy',
      as: 'lastUpdatedByUser',
    });
  };

  return RequisitionItemHistoryModel;
};
