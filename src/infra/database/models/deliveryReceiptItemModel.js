const {
  DELIVERY_ITEM_STATUSES,
} = require('../../../domain/constants/deliveryReceiptItemConstants');
const { item } = require('../../../domain/entities');
module.exports = (sequelize, Sequelize) => {
  const DeliveryReceiptItemModel = sequelize.define(
    'delivery_receipt_items',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      drId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'delivery_receipts',
          key: 'id',
        },
      },
      poId: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      poItemId: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      itemId: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      itemType: {
        type: Sequelize.STRING(20),
        allowNull: true,
      },
      itemDes: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      qtyOrdered: {
        type: Sequelize.DECIMAL(10, 3),
        allowNull: false,
        set(value) {
          const fixedValue = parseFloat(value || 0).toFixed(3);
          this.setDataValue('qtyOrdered', fixedValue);
        },
        get() {
          const rawValue = this.getDataValue('qtyOrdered');
          return parseFloat(rawValue || 0);
        },
      },
      qtyDelivered: {
        type: Sequelize.DECIMAL(10, 3),
        allowNull: true,
        set(value) {
          const fixedValue = parseFloat(value || 0).toFixed(3);
          this.setDataValue('qtyDelivered', fixedValue);
        },
        get() {
          const rawValue = this.getDataValue('qtyDelivered');
          return parseFloat(rawValue || 0);
        },
      },
      qtyReturned: {
        type: Sequelize.DECIMAL(10, 3),
        allowNull: true,
        set(value) {
          const fixedValue = parseFloat(value || 0).toFixed(3);
          this.setDataValue('qtyReturned', fixedValue);
        },
        get() {
          const rawValue = this.getDataValue('qtyReturned');
          return parseFloat(rawValue || 0);
        },
      },
      hasReturns: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      unit: {
        type: Sequelize.STRING(5),
        allowNull: false,
      },
      dateDelivered: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      deliveryStatus: {
        type: Sequelize.STRING(50),
        allowNull: true,
      },
      notes: {
        type: Sequelize.STRING(60),
        allowNull: true,
      },
      isLatestStatusReturned: {
        type: Sequelize.VIRTUAL,
        get() {
          return (
            this.deliveryStatus ===
              DELIVERY_ITEM_STATUSES.FULLY_DELIVERED_WITH_RETURNS ||
            this.deliveryStatus ===
              DELIVERY_ITEM_STATUSES.PARTIALLY_DELIVERED_WITH_RETURNS
          );
        },
      },
    },
    {
      timestamps: true,
      underscored: true,
    },
  );

  DeliveryReceiptItemModel.associate = (models) => {
    DeliveryReceiptItemModel.belongsTo(models.deliveryReceiptModel, {
      foreignKey: 'drId',
      as: 'deliveryReceipt',
      onDelete: 'CASCADE',
    });

    DeliveryReceiptItemModel.hasMany(models.deliveryReceiptItemHistoryModel, {
      foreignKey: 'deliveryReceiptItemId',
      as: 'history',
    });

    DeliveryReceiptItemModel.belongsTo(models.purchaseOrderItemModel, {
      foreignKey: 'poItemId',
      as: 'purchaseOrderItem',
    });

    DeliveryReceiptItemModel.belongsTo(models.ofmListItemModel, {
      foreignKey: 'itemId',
      as: 'ofmItem',
    });

    DeliveryReceiptItemModel.belongsTo(models.itemModel, {
      foreignKey: 'itemId',
      as: 'item',
    });

    DeliveryReceiptItemModel.belongsTo(models.nonOfmItemModel, {
      foreignKey: 'itemId',
      as: 'nonOfmItem',
    });
  };

  const setItemType = async (item) => {
    try {
      const purchaseOrder = await sequelize.model('purchase_orders').findOne({
        where: {
          id: item.poId,
        },
        include: [
          {
            model: sequelize.model('requisitions'),
            as: 'requisition',
          },
        ],
      });

      if (purchaseOrder.requisition) {
        const type = purchaseOrder.requisition.type;
        const typeMap = {
          ofm: 'ofm',
          'ofm-tom': 'ofm',
          'non-ofm': 'non-ofm',
          'non-ofm-tom': 'non-ofm',
        };
        item.itemType = typeMap[type] || null;
      }
    } catch (error) {
      console.error(
        'HOOK_ERROR - deliveryReceiptItemModel - setItemType: ',
        error.stack,
      );
    }
  };

  const setDeliveryStatus = (item) => {
    try {
      if (item.qtyOrdered === item.qtyDelivered) {
        item.deliveryStatus = DELIVERY_ITEM_STATUSES.FULLY_DELIVERED;
        if (item.hasReturns) {
          item.deliveryStatus =
            DELIVERY_ITEM_STATUSES.FULLY_DELIVERED_WITH_RETURNS;
        }
      } else if (item.qtyDelivered < item.qtyOrdered) {
        item.deliveryStatus = DELIVERY_ITEM_STATUSES.PARTIALLY_DELIVERED;
        if (item.qtyReturned > 0) {
          item.deliveryStatus =
            DELIVERY_ITEM_STATUSES.PARTIALLY_DELIVERED_WITH_RETURNS;
          item.hasReturns = true;
        }
      }
    } catch (error) {
      console.error(
        'HOOK_ERROR - deliveryReceiptItemModel - setDeliveryStatus: ',
        error.stack,
      );
    }
  };

  const setLatestDate = async (item, options) => {
    try {
      const transaction = options?.transaction || null;
      if (item.changed('dateDelivered')) {
        const deliveryReceipt = await sequelize
          .model('delivery_receipts')
          .findOne({
            where: {
              id: item.drId,
            },
            transaction,
          });

        if (
          deliveryReceipt &&
          item.dateDelivered > deliveryReceipt.latestDeliveryDate
        ) {
          deliveryReceipt.latestDeliveryDate = item.dateDelivered;
          await deliveryReceipt.save({ transaction });
        }
      }
    } catch (error) {
      console.error(
        'HOOK_ERROR - deliveryReceiptItemModel - setLatestDate: ',
        error.stack,
      );
    }
  };

  const createRequisitionDeliveryHistoryRecord = async (item, options) => {
    try {
      const { transaction } = options;
      const deliveryReceipt = await sequelize
        .model('delivery_receipts')
        .findOne({
          where: {
            id: item.drId,
          },
        });

      const purchaseOrder = await sequelize.model('purchase_orders').findOne({
        attributes: ['createdAt'],
        where: {
          id: deliveryReceipt.poId,
        },
      });

      if (item.changed('qtyReturned')) {
        let itemName = '';
        if (item.itemType === 'ofm' || item.itemType === 'ofm-tom') {
          const ofmItemResult = await sequelize.model('items').findOne({
            where: {
              id: item.itemId,
            },
          });
          itemName = ofmItemResult.itmDes;
        } else {
          const nonOfmItemResult = await sequelize
            .model('non_ofm_items')
            .findOne({
              where: {
                id: item.itemId,
              },
            });
          itemName = nonOfmItemResult.itemName;
        }

        await sequelize.model('requisition_return_histories').create(
          {
            requisitionId: deliveryReceipt.requisitionId,
            drNumber: deliveryReceipt.actualNumber,
            supplier: deliveryReceipt.supplier,
            item: itemName,
            dateOrdered: purchaseOrder.createdAt,
            quantityOrdered: item.qtyOrdered,
            quantityReturned: item.qtyReturned,
            returnDate: item.dateDelivered,
            status:
              item.qtyReturned + item.qtyDelivered === item.qtyOrdered
                ? DELIVERY_ITEM_STATUSES.FULLY_DELIVERED_WITH_RETURNS
                : DELIVERY_ITEM_STATUSES.PARTIALLY_DELIVERED_WITH_RETURNS,
          },
          {
            transaction,
          },
        );
      } else if (
        item.changed('qtyDelivered') ||
        item.changed('dateDelivered')
      ) {
        await sequelize.model('requisition_delivery_histories').create(
          {
            requisitionId: deliveryReceipt.requisitionId,
            drNumber: deliveryReceipt.actualNumber,
            supplier: deliveryReceipt.supplier,
            dateOrdered: purchaseOrder.createdAt,
            quantityOrdered: item.qtyOrdered,
            quantityDelivered: item.qtyDelivered,
            dateDelivered: item.dateDelivered,
            status:
              item.qtyDelivered < item.qtyOrdered
                ? DELIVERY_ITEM_STATUSES.PARTIALLY_DELIVERED
                : DELIVERY_ITEM_STATUSES.FULLY_DELIVERED,
          },
          {
            transaction,
          },
        );
      }
    } catch (error) {
      console.error(
        'HOOK_ERROR - deliveryReceiptItemModel - createRequisitionDeliveryHistoryRecord: ',
        error.stack,
      );
    }
  };

  const createDeliveryItemHistoryRecord = async (item, { transaction }) => {
    try {
      if (
        item.changed('qtyReturned') &&
        item.qtyReturned === 0 &&
        item.changed('hasReturns') &&
        item.hasReturns === false
      ) {
        await sequelize.model('delivery_receipt_items_history').create(
          {
            deliveryReceiptItemId: item.id,
            qtyOrdered: item.qtyOrdered,
            qtyDelivered: item.qtyDelivered,
            qtyReturned: item.qtyReturned,
            dateDelivered: item.dateDelivered,
            status: DELIVERY_ITEM_STATUSES.CANCELLED_RETURNS,
          },
          {
            transaction,
          },
        );
      } else if (
        item.changed('deliveryStatus') ||
        item.changed('dateDelivered')
      ) {
        await sequelize.model('delivery_receipt_items_history').create(
          {
            deliveryReceiptItemId: item.id,
            qtyOrdered: item.qtyOrdered,
            qtyDelivered: item.qtyDelivered,
            qtyReturned: item.qtyReturned,
            dateDelivered: item.dateDelivered,
            status: item.deliveryStatus,
          },
          {
            transaction,
          },
        );
      }
    } catch (error) {
      console.error(
        'HOOK_ERROR - deliveryReceiptItemModel - createDeliveryItemHistoryRecord: ',
        error.stack,
      );
    }
  };

  const updateItemHistoryRecord = async (
    instance,
    { parentRecord, transaction },
  ) => {
    try {
      if (
        instance.changed('qtyDelivered') ||
        instance.changed('dateDelivered')
      ) {
        let historyRecord;
        if (parentRecord) {
          const requisition = await sequelize.model('requisitions').findOne({
            where: {
              id: parentRecord.requisitionId,
            },
          });
          historyRecord = await sequelize.model('histories').findOne({
            where: {
              rsLetter: requisition.rsLetter,
              rsNumber: requisition.rsNumber,
              companyId: requisition.companyId,
              itemId: instance.itemId,
            },
          });
        } else {
          historyRecord = await sequelize.model('histories').findOne({
            where: {
              drItemId: instance.id,
              itemId: instance.itemId,
            },
          });
        }

        if (historyRecord) {
          await historyRecord.update(
            {
              quantityDelivered: instance.qtyDelivered,
              dateDelivered: instance.dateDelivered,
            },
            { transaction },
          );
        }
      }
    } catch (error) {
      console.log(
        'HOOK_ERROR - deliveryReceiptItemModel - updateItemHistoryRecord: ',
        error.stack,
      );
    }
  };

  const setLatestStatus = async (item, options) => {
    const { transaction } = options;
    try {
      if (item.changed('qtyDelivered') || item.changed('qtyReturned')) {
        const items = await sequelize.model('delivery_receipt_items').findAll({
          attributes: [
            'id',
            'qtyOrdered',
            'qtyDelivered',
            'qtyReturned',
            'hasReturns',
          ],
          where: {
            drId: item.drId,
          },
        });

        if (items) {
          const itemStatuses = items.map((item) => {
            let status = '';

            if (item.qtyOrdered === item.qtyDelivered) {
              status = item.hasReturns
                ? DELIVERY_ITEM_STATUSES.FULLY_DELIVERED_WITH_RETURNS
                : DELIVERY_ITEM_STATUSES.FULLY_DELIVERED;
            } else if (item.qtyDelivered < item.qtyOrdered) {
              status =
                item.qtyReturned > 0 || item.hasReturns
                  ? DELIVERY_ITEM_STATUSES.PARTIALLY_DELIVERED_WITH_RETURNS
                  : DELIVERY_ITEM_STATUSES.PARTIALLY_DELIVERED;
            }

            return status;
          });

          const partiallyDelivered = itemStatuses.some(
            (s) =>
              s === DELIVERY_ITEM_STATUSES.PARTIALLY_DELIVERED ||
              s === DELIVERY_ITEM_STATUSES.PARTIALLY_DELIVERED_WITH_RETURNS,
          );
          const fullyDelivered = itemStatuses.every(
            (s) =>
              s === DELIVERY_ITEM_STATUSES.FULLY_DELIVERED ||
              s === DELIVERY_ITEM_STATUSES.FULLY_DELIVERED_WITH_RETURNS,
          );

          let deliveryReceiptStatus;
          if (partiallyDelivered) {
            deliveryReceiptStatus = itemStatuses.some(
              (s) =>
                s === DELIVERY_ITEM_STATUSES.PARTIALLY_DELIVERED_WITH_RETURNS,
            )
              ? DELIVERY_ITEM_STATUSES.PARTIALLY_DELIVERED_WITH_RETURNS
              : DELIVERY_ITEM_STATUSES.PARTIALLY_DELIVERED;
          } else if (fullyDelivered) {
            deliveryReceiptStatus = itemStatuses.some(
              (s) => s === DELIVERY_ITEM_STATUSES.FULLY_DELIVERED_WITH_RETURNS,
            )
              ? DELIVERY_ITEM_STATUSES.FULLY_DELIVERED_WITH_RETURNS
              : DELIVERY_ITEM_STATUSES.FULLY_DELIVERED;
          } else {
            deliveryReceiptStatus = null;
          }

          await sequelize
            .model('delivery_receipts')
            .update(
              { latestDeliveryStatus: deliveryReceiptStatus },
              { where: { id: item.drId }, transaction },
            );
        }
      }
    } catch (error) {
      console.log(
        'HOOK_ERROR - deliveryReceiptItemModel - setLatestStatus: ',
        error.stack,
      );
    }
  };

  DeliveryReceiptItemModel.beforeCreate(setItemType);
  DeliveryReceiptItemModel.beforeCreate(setDeliveryStatus);
  DeliveryReceiptItemModel.afterCreate(createDeliveryItemHistoryRecord);
  DeliveryReceiptItemModel.afterCreate(setLatestDate);
  DeliveryReceiptItemModel.afterCreate(updateItemHistoryRecord);

  DeliveryReceiptItemModel.beforeUpdate(setItemType);
  DeliveryReceiptItemModel.beforeUpdate(setDeliveryStatus);
  DeliveryReceiptItemModel.beforeUpdate(createRequisitionDeliveryHistoryRecord);
  DeliveryReceiptItemModel.afterUpdate(setLatestStatus);
  DeliveryReceiptItemModel.afterUpdate(createDeliveryItemHistoryRecord);
  DeliveryReceiptItemModel.afterUpdate(setLatestDate);
  DeliveryReceiptItemModel.afterUpdate(updateItemHistoryRecord);

  DeliveryReceiptItemModel.beforeDestroy(async (item, { transaction }) => {
    try {
      await sequelize.model('delivery_receipt_items_history').destroy(
        {
          where: {
            deliveryReceiptItemId: item.id,
          },
        },
        { transaction },
      );
    } catch (error) {
      console.error(
        'HOOK_ERROR - DeliveryReceiptItemModel.beforeDestroy: ',
        error.stack,
      );
    }
  });

  const createRequisitionItemHistoryRecord = async (
    deliveryReceiptItem,
    options,
  ) => {
    const { transaction, userId = null } = options;

    // Try to get userId from transaction context if not in options
    let actualUserId = userId;
    if (!actualUserId && transaction && transaction.userId) {
      actualUserId = transaction.userId;
    }

    try {
      const purchaseOrderItem = await sequelize
        .model('purchase_order_items')
        .findOne({
          attributes: ['requisitionItemListId'],
          where: { purchaseOrderId: deliveryReceiptItem.poId },
        });

      const requisitionItemList = await sequelize
        .model('requisition_item_lists')
        .findOne({
          attributes: ['requisitionId', 'quantity'],
          where: { id: purchaseOrderItem.requisitionItemListId },
        });

      await sequelize.model('requisition_item_histories').create(
        {
          requisitionId: requisitionItemList.requisitionId,
          item: deliveryReceiptItem.itemDes,
          quantityRequested: requisitionItemList.quantity,
          quantityOrdered: deliveryReceiptItem.qtyOrdered || 0,
          quantityDelivered: deliveryReceiptItem.qtyDelivered || 0,
          ...(actualUserId ? { lastUpdatedBy: actualUserId } : {}),
        },
        { transaction },
      );
    } catch (error) {
      console.error(
        'HOOK_ERROR in createRequisitionItemHistoryRecord: ',
        error.stack,
      );
    }
  };

  DeliveryReceiptItemModel.afterCreate(createRequisitionItemHistoryRecord);
  DeliveryReceiptItemModel.afterUpdate(createRequisitionItemHistoryRecord);

  return DeliveryReceiptItemModel;
};
