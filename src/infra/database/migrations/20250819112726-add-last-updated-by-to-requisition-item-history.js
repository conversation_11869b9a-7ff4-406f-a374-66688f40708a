'use strict';
const { withTimescaleDBCompression } = require('../utils/timescale-db-migration-helper');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // check if the requisition_item_histories last_updated_by already exist. If exists complete the migration
      const tableInfo = await queryInterface.describeTable('requisition_item_histories');
      if (tableInfo.last_updated_by) {
        console.log('skipping last_updated_by already created');
        return true;
      }

      // Decompress all compressed chunks before removing the column
      console.log('Removing compressed hypertable for requisition_item_histories...');
      console.log('Setting timescaledb.compress to false');
      await queryInterface.sequelize.query(`
        -- Step 1: remove compressed hyper table
        DO $$
        DECLARE
            chunk_record record;
        BEGIN
            FOR chunk_record IN
                SELECT chunk_schema, chunk_name
                FROM timescaledb_information.chunks
                WHERE
                    hypertable_schema = 'public'
                    AND hypertable_name = 'requisition_item_histories'
                    AND is_compressed = true
            LOOP
                EXECUTE format('SELECT decompress_chunk(''%s.%s'')', chunk_record.chunk_schema, chunk_record.chunk_name);
            END LOOP;
        END $$;

        -- Step 2: set the compress hypertable to false
        ALTER TABLE requisition_item_histories SET (timescaledb.compress = false);
      `, { transaction });

      await withTimescaleDBCompression(queryInterface, transaction, 'requisition_item_histories', async () => {
        console.log('Adding last_updated_by column to requisition_item_histories...');

        await queryInterface.addColumn('requisition_item_histories', 'last_updated_by', {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: 'users',
            key: 'id',
          },
          onUpdate: 'CASCADE',
          onDelete: 'SET NULL',
        }, { transaction });
      });

      console.log('Setting timescaledb.compress to true');
      await queryInterface.sequelize.query(`
        -- Step 4: Add back the compress hypertable
        ALTER TABLE requisition_item_histories SET (timescaledb.compress = true);
      `, { transaction });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();

    try {
      await withTimescaleDBCompression(queryInterface, transaction, 'requisition_item_histories', async () => {
        await queryInterface.removeColumn(
          'requisition_item_histories',
          'last_updated_by',
          { transaction }
        );
      });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
};
