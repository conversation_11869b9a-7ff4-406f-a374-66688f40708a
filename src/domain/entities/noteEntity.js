const { z } = require('zod');
const { note } = require('../constants');
const { stringFieldError } = require('../../app/utils');

const normalize = (s) =>
  s.normalize('NFKC')
   .replace(/[\u0000-\u001F\u007F]+/g, ' ') /// control chars -> space
   .replace(/\s+/g, ' ')
   .trim();

const createNoteSchema = z
  .object({
    model: z.enum(Object.values(note.MODELS)),
    modelId: z
      .number()
      .refine((val) => Number.isInteger(Number(val)) && Number(val) > 0, {
        message: 'Model ID is invalid.',
      }),
    userName: z.string().optional(),
    userType: z.enum(Object.values(note.USER_TYPES)).optional().nullable(),
    commentType: z.enum(Object.values(note.COMMENT_TYPES)),
    note: z
      .string()
      .transform(v => normalize(v))
      .pipe(
        z.string()
        .max(100, 'Note must be at most 100 characters')
        .regex(
          note.REGEX,
          'Notes can only contain letters, numbers, spaces and special characters',
        )
      )
      .optional(),
  })
  .strict();

const getNotesParamsSchema = z
  .object({
    model: z.enum(Object.values(note.MODELS)),
    modelId: z
      .string()
      .refine((val) => Number.isInteger(Number(val)) && Number(val) > 0, {
        message: 'Model ID is invalid.',
      }),
  })
  .strict();

const getNotesParamsQuery = z
  .object({
    dateFrom: z.coerce.date().optional(),
    dateTo: z.coerce.date().optional(),
  })
  .strict();

const rejectReasonSchema = z
  .object({
    rejectReason: z
      .string(stringFieldError('Reject reason'))
      .min(1, { message: 'Reject Reason is required' })
      .max(100, { message: 'Reason must not exceed 100 characters' })
      .regex(
        note.REGEX,
        'Notes can only contain letters, numbers, spaces and special characters',
      ),
  })
  .strict();

const approveReasonSchema = z
  .object({
    approveReason: z
      .string(stringFieldError('Approve reason'))
      .transform((val) => (val === '' ? undefined : val))
      .superRefine((val, ctx) => {
        // Skip validation if value is undefined or null
        if (val === undefined || val === null || val === '') return;

        // Check length
        if (val.length > 100) {
          ctx.addIssue({
            code: z.ZodIssueCode.too_big,
            maximum: 100,
            type: 'string',
            inclusive: true,
            message: 'Reason must not exceed 100 characters',
          });
          return;
        }

        // Check characters
        const validCharsRegex = note.REGEX;

        if (!validCharsRegex.test(val)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message:
              'Notes can only contain letters, numbers, spaces and special characters',
          });
          return;
        }
      })
      .nullable()
      .optional(),
  })
  .strict();

module.exports = {
  createNoteSchema,
  getNotesParamsSchema,
  getNotesParamsQuery,
  approveReasonSchema,
  rejectReasonSchema,
};
